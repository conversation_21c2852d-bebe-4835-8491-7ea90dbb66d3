<template>
  <div class="document-preview">
    <!-- 头部导航 -->
    <van-nav-bar
      :title="documentTitle"
      left-text=""
      left-arrow
      @click-left="$router.go(-1)"
    >
      <template #right>
        <van-icon name="share" @click="shareDocument" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-center">
      加载中...
    </van-loading>

    <!-- Word文档预览 -->
    <div v-else-if="isWordDocument" class="word-document-preview">
      <div class="document-info">
        <div class="doc-icon-large">
          <van-icon name="notes-o" size="48" color="#1890ff" />
        </div>
        <div class="doc-details">
          <h3>{{ documentTitle }}</h3>
          <p class="doc-description">{{ documentDescription }}</p>
          <div class="doc-meta">
            <span>文件大小: {{ documentSize }}</span>
            <span>更新时间: {{ documentUpdateTime }}</span>
          </div>
        </div>
      </div>

      <div class="preview-actions">
        <van-button
          type="primary"
          size="large"
          block
          color="#428ffc"
          @click="downloadDocument"
          :loading="downloading"
        >
          <van-icon name="down" />
          下载文档
        </van-button>

        <van-button
          type="default"
          size="large"
          block
          @click="showPreviewOptions"
          style="margin-top: 12px;"
        >
          <van-icon name="eye-o" />
          在线预览
        </van-button>

        <van-button
          type="info"
          size="large"
          block
          @click="showDocumentInfo"
          style="margin-top: 12px;"
        >
          <van-icon name="info-o" />
          文档详情
        </van-button>
      </div>

      <!-- 文档详情展示 -->
      <div v-if="showDocInfo" class="document-details">
        <van-cell-group>
          <van-cell title="文档名称" :value="documentTitle" />
          <van-cell title="文件大小" :value="documentSize" />
          <van-cell title="更新时间" :value="documentUpdateTime" />
          <van-cell title="文件格式" value="Microsoft Word 文档" />
          <van-cell title="兼容性" value="支持 Office 2007 及以上版本" />
        </van-cell-group>

        <div class="usage-tips">
          <h4>使用建议：</h4>
          <ul>
            <li>建议使用 Microsoft Office、WPS 或其他兼容软件打开</li>
            <li>移动设备可使用 Office Mobile 应用</li>
            <li>如需编辑，请下载到本地后操作</li>
            <li>在线预览可能存在格式差异，以下载版本为准</li>
          </ul>
        </div>
      </div>

      <div class="preview-tips">
        <van-notice-bar
          text="提示：Word文档建议下载后使用Office软件打开，或选择合适的在线预览方式"
          left-icon="info-o"
          :scrollable="false"
        />
      </div>

      <!-- 预览方式选择弹窗 -->
      <van-action-sheet
        v-model="showPreviewSheet"
        title="选择预览方式"
        :actions="previewActions"
        @select="onPreviewSelect"
        cancel-text="取消"
      />

      <!-- 预览失败提示弹窗 -->
      <van-dialog
        v-model="showPreviewDialog"
        title="预览说明"
        :message="previewMessage"
        show-cancel-button
        confirm-button-text="继续尝试"
        cancel-button-text="直接下载"
        @confirm="retryPreview"
        @cancel="downloadDocument"
      />
    </div>

    <!-- Markdown文档内容 -->
    <div v-else-if="documentContent" class="document-content">
      <div class="content-wrapper" v-html="renderedContent"></div>
    </div>

    <!-- 错误状态 -->
    <van-empty
      v-else
      description="文档加载失败"
      image="error"
    >
      <van-button
        type="primary"
        size="small"
        @click="loadDocument"
      >
        重新加载
      </van-button>
    </van-empty>

    <!-- 底部操作栏 -->
    <div class="bottom-actions" v-if="!loading && documentContent">
      <van-button
        type="default"
        size="small"
        @click="toggleFontSize"
      >
        {{ fontSize === 'normal' ? '大字体' : '正常字体' }}
      </van-button>
      <van-button
        type="primary"
        size="small"
        @click="scrollToTop"
      >
        回到顶部
      </van-button>
    </div>
  </div>
</template>

<script>
import { getDocumentContent } from '@/api/help'

export default {
  name: 'DocumentPreview',
  data() {
    return {
      loading: false,
      documentContent: '',
      documentTitle: '',
      documentDescription: '',
      documentSize: '',
      documentUpdateTime: '',
      fontSize: 'normal',
      downloading: false,
      isWordDocument: false,
      showPreviewSheet: false,
      showPreviewDialog: false,
      previewMessage: '',
      currentPreviewUrl: '',
      showDocInfo: false,
      previewActions: [
        {
          name: 'Microsoft Office Online',
          subname: '微软官方在线预览服务',
          value: 'office'
        },
        {
          name: 'Google Docs Viewer',
          subname: 'Google文档查看器',
          value: 'google'
        },
        {
          name: '腾讯文档预览',
          subname: '腾讯在线文档服务',
          value: 'tencent'
        },
        {
          name: '直接下载',
          subname: '下载到本地查看',
          value: 'download'
        }
      ]
    }
  },
  computed: {
    renderedContent() {
      if (!this.documentContent) return ''

      // 简单的Markdown渲染（实际项目中建议使用专业的Markdown解析库）
      let html = this.documentContent
        // 标题
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        // 粗体
        .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
        // 斜体
        .replace(/\*(.*)\*/gim, '<em>$1</em>')
        // 代码块
        .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
        // 行内代码
        .replace(/`([^`]*)`/gim, '<code>$1</code>')
        // 链接
        .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2" target="_blank">$1</a>')
        // 换行
        .replace(/\n/gim, '<br>')
        // 列表
        .replace(/^\- (.*$)/gim, '<li>$1</li>')
        .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')

      // 包装列表项
      html = html.replace(/(<li>.*<\/li>)/gims, '<ul>$1</ul>')

      return html
    }
  },
  mounted() {
    this.documentTitle = this.$route.query.title || '文档预览'
    this.documentDescription = this.$route.query.description || ''
    this.documentSize = this.$route.query.size || ''
    this.documentUpdateTime = this.$route.query.updateTime || ''

    const filename = this.$route.params.filename
    this.isWordDocument = filename && filename.endsWith('.docx')

    if (!this.isWordDocument) {
      this.loadDocument()
    }
  },
  methods: {
    async loadDocument() {
      this.loading = true
      try {
        const filename = this.$route.params.filename

        // 模拟从服务器获取文档内容
        // const response = await getDocumentContent(filename)
        // this.documentContent = response.data

        // 这里模拟加载本地文档内容
        const content = await this.loadLocalDocument(filename)
        this.documentContent = content

      } catch (error) {
        console.error('加载文档失败:', error)
        this.$toast.fail('文档加载失败')
      } finally {
        this.loading = false
      }
    },

    async loadLocalDocument(filename) {
      // 模拟文档内容（实际项目中应该从服务器获取）
      const documents = {
        '用户手册.md': `# 用户手册

## 概述
欢迎使用移动综合入口应用！本手册将帮助您快速了解和使用应用的各项功能。

## 主要功能

### 1. 首页
- 查看待办任务
- 快速访问常用功能
- 查看系统通知

### 2. 案卷档案
- 查看案件列表
- 处理待办案件
- 查看案件详情

### 3. 考勤管理
- 采集员考勤
- 站前考勤
- 考勤记录查询

### 4. 人员定位
- 实时查看人员位置
- 历史轨迹查询
- 位置信息管理

### 5. 个人中心
- 个人信息管理
- 系统设置
- 数据同步
- 清除缓存

## 操作指南

### 登录系统
1. 打开应用
2. 输入用户名和密码
3. 点击登录按钮

### 处理案件
1. 进入案卷档案
2. 选择待处理案件
3. 填写处理信息
4. 提交处理结果

## 常见问题

### Q: 忘记密码怎么办？
A: 请联系系统管理员重置密码。

### Q: 应用无法正常加载？
A: 请检查网络连接，或尝试清除缓存后重新登录。`,

        '操作指南.md': `# 操作指南

## 快速开始

### 系统要求
- 支持的浏览器：Chrome、Safari、Firefox
- 网络要求：稳定的网络连接
- 设备要求：支持GPS定位功能

### 首次使用
1. 获取账号信息
2. 下载并安装应用
3. 使用账号登录
4. 完成初始设置

## 详细操作步骤

### 案件处理流程

#### 日常巡查
1. 进入"案卷档案"
2. 选择"日常任务"
3. 查看任务列表
4. 点击具体任务进行处理
5. 填写巡查记录
6. 上传相关照片
7. 提交处理结果`,

        '常见问题.md': `# 常见问题解答

## 登录相关

### Q1: 无法登录系统
**问题描述：** 输入正确的用户名和密码后仍无法登录

**解决方案：**
1. 检查网络连接是否正常
2. 确认用户名和密码是否正确
3. 清除浏览器缓存后重试
4. 联系管理员确认账号状态

### Q2: 登录后页面空白
**问题描述：** 成功登录后页面显示空白

**解决方案：**
1. 刷新页面重试
2. 清除浏览器缓存和Cookie
3. 检查浏览器版本是否支持
4. 尝试使用其他浏览器`,

        '系统更新日志.md': `# 系统更新日志

## 版本 2.1.0 (2024-01-15)

### 新增功能
- 新增帮助文档功能，支持在线查看操作指南
- 新增文档预览功能，支持Markdown格式文档
- 优化个人中心界面，增加帮助入口

### 功能优化
- 优化案件处理流程，提升操作体验
- 改进GPS定位精度，提高考勤准确性
- 优化数据同步机制，提升同步效率

### 问题修复
- 修复部分机型上图片上传失败的问题
- 修复网络异常时数据丢失的问题
- 修复考勤打卡时偶现的定位错误`
      }

      return documents[filename] || '文档内容未找到'
    },

    shareDocument() {
      // 分享文档
      if (navigator.share) {
        navigator.share({
          title: this.documentTitle,
          text: '查看帮助文档',
          url: window.location.href
        })
      } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
          this.$toast.success('链接已复制到剪贴板')
        }).catch(() => {
          this.$toast.fail('分享失败')
        })
      }
    },

    toggleFontSize() {
      this.fontSize = this.fontSize === 'normal' ? 'large' : 'normal'
      const contentWrapper = document.querySelector('.content-wrapper')
      if (contentWrapper) {
        contentWrapper.classList.toggle('large-font', this.fontSize === 'large')
      }
    },

    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },

    async downloadDocument() {
      this.downloading = true
      try {
        const filename = this.$route.params.filename

        // 显示下载提示
        this.$toast.loading({
          message: '准备下载...',
          forbidClick: true,
          duration: 0
        })

        // 构建文档下载URL - 使用相对路径
        const downloadUrl = `./src/views/personCenter/docs/${filename}`

        // 尝试多种下载方式
        if (this.isMobile()) {
          // 移动端：直接打开链接
          window.open(downloadUrl, '_blank')
          this.$toast.clear()
          this.$toast.success('已在新窗口打开文档')
        } else {
          // PC端：创建下载链接
          const link = document.createElement('a')
          link.href = downloadUrl
          link.download = filename
          link.style.display = 'none'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          this.$toast.clear()
          this.$toast.success('开始下载文档')
        }

        // 记录下载行为
        this.recordDownload(filename)

      } catch (error) {
        console.error('下载文档失败:', error)
        this.$toast.clear()

        // 提供备用下载方案
        this.$dialog.confirm({
          title: '下载失败',
          message: '自动下载失败，是否尝试在新窗口打开文档？',
          confirmButtonText: '打开',
          cancelButtonText: '取消'
        }).then(() => {
          const filename = this.$route.params.filename
          const downloadUrl = `./src/views/personCenter/docs/${filename}`
          window.open(downloadUrl, '_blank')
        }).catch(() => {
          // 用户取消
        })
      } finally {
        this.downloading = false
      }
    },

    isMobile() {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    },

    recordDownload(filename) {
      // 记录下载统计（可以发送到后端）
      console.log('文档下载记录:', {
        filename,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
      })
    },

    showPreviewOptions() {
      this.showPreviewSheet = true
    },

    onPreviewSelect(action) {
      this.showPreviewSheet = false

      if (action.value === 'download') {
        this.downloadDocument()
        return
      }

      this.openInBrowser(action.value)
    },

    openInBrowser(type = 'office') {
      const filename = this.$route.params.filename
      const fileUrl = encodeURIComponent(window.location.origin + `/src/views/personCenter/docs/${filename}`)

      let previewUrl = ''
      let serviceName = ''

      switch (type) {
        case 'office':
          previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${fileUrl}`
          serviceName = 'Microsoft Office Online'
          break
        case 'google':
          previewUrl = `https://docs.google.com/viewer?url=${fileUrl}&embedded=true`
          serviceName = 'Google Docs Viewer'
          break
        case 'tencent':
          previewUrl = `https://docs.qq.com/scenario/link.html?url=${fileUrl}`
          serviceName = 'QQ文档预览'
          break
        default:
          previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${fileUrl}`
          serviceName = 'Microsoft Office Online'
      }

      this.currentPreviewUrl = previewUrl

      // 尝试在新窗口打开预览
      const previewWindow = window.open(previewUrl, '_blank')

      // 检查窗口是否成功打开
      setTimeout(() => {
        if (!previewWindow || previewWindow.closed) {
          this.showPreviewFailDialog(serviceName)
        } else {
          // 监听窗口加载错误
          previewWindow.addEventListener('error', () => {
            this.showPreviewFailDialog(serviceName)
          })
        }
      }, 1000)
    },

    showPreviewFailDialog(serviceName) {
      this.previewMessage = `${serviceName}预览服务可能无法访问或不支持此文档格式。\n\n建议：\n1. 尝试其他预览方式\n2. 直接下载文档到本地查看\n3. 检查网络连接是否正常`
      this.showPreviewDialog = true
    },

    retryPreview() {
      this.showPreviewDialog = false
      this.showPreviewOptions()
    },

    showDocumentInfo() {
      this.showDocInfo = !this.showDocInfo
    }
  }
}
</script>

<style lang="scss" scoped>
.document-preview {
  min-height: 100vh;
  background: #fff;

  .loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  .word-document-preview {
    padding: 20px;

    .document-info {
      background: #fff;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      text-align: center;

      .doc-icon-large {
        margin-bottom: 16px;
      }

      .doc-details {
        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }

        .doc-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 16px;
        }

        .doc-meta {
          display: flex;
          justify-content: center;
          gap: 20px;
          font-size: 12px;
          color: #999;

          span {
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .preview-actions {
      margin-bottom: 20px;

      .van-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        height: 48px;
        font-size: 16px;
      }
    }

    .preview-tips {
      .van-notice-bar {
        border-radius: 8px;
        background: #f0f9ff;
        border: 1px solid #e1f5fe;
      }
    }

    .document-details {
      margin-top: 20px;

      .van-cell-group {
        margin-bottom: 16px;
      }

      .usage-tips {
        background: #fff;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 12px;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }

  .document-content {
    padding: 16px;
    padding-bottom: 80px; // 为底部操作栏留空间

    .content-wrapper {
      line-height: 1.6;
      color: #333;

      &.large-font {
        font-size: 18px;

        :deep(h1) { font-size: 28px; }
        :deep(h2) { font-size: 24px; }
        :deep(h3) { font-size: 20px; }
      }

      :deep(h1) {
        font-size: 24px;
        font-weight: bold;
        margin: 20px 0 16px 0;
        color: #1a1a1a;
        border-bottom: 2px solid #428ffc;
        padding-bottom: 8px;
      }

      :deep(h2) {
        font-size: 20px;
        font-weight: bold;
        margin: 18px 0 14px 0;
        color: #333;
      }

      :deep(h3) {
        font-size: 16px;
        font-weight: bold;
        margin: 16px 0 12px 0;
        color: #555;
      }

      :deep(p) {
        margin: 12px 0;
      }

      :deep(ul) {
        margin: 12px 0;
        padding-left: 20px;

        li {
          margin: 6px 0;
          list-style-type: disc;
        }
      }

      :deep(ol) {
        margin: 12px 0;
        padding-left: 20px;

        li {
          margin: 6px 0;
          list-style-type: decimal;
        }
      }

      :deep(code) {
        background: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }

      :deep(pre) {
        background: #f5f5f5;
        padding: 12px;
        border-radius: 6px;
        overflow-x: auto;
        margin: 12px 0;

        code {
          background: none;
          padding: 0;
        }
      }

      :deep(strong) {
        font-weight: bold;
        color: #1a1a1a;
      }

      :deep(em) {
        font-style: italic;
      }

      :deep(a) {
        color: #428ffc;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 12px 16px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

    .van-button {
      flex: 1;
      margin: 0 4px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
