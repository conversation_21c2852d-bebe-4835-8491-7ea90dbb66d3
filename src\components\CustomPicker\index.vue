<template>
  <div class="custom-picker">
    <!-- 触发按钮 -->
    <div class="picker-trigger" @click="showPicker">
      <slot name="trigger">
        <div class="selected-value" :class="{valueColor: showValue}">
          {{ showValue || placeholder }}
        </div>
        <van-icon name="arrow" />
      </slot>
    </div>

    <!-- 弹出层选择器 -->
    <van-popup
      v-model="isVisible"
      round
      position="bottom"
    >
      <van-picker
        :columns="columns"
        :default-index="defaultIndex"
        show-toolbar
        :title="title"
        :loading="loading"
        @confirm="onConfirm"
        @cancel="onCancel"
        @change="onChange"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'CustomPicker',
  props: {
    // 选项数据
    columns: {
      type: Array,
      default: () => []
    },
    // 选中的值
    value: {
      type: [String, Number],
      default: ''
    },
    // 标题
    title: {
      type: String,
      default: '请选择'
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      isVisible: false,
      defaultIndex: 0
    }
  },

  computed: {
    // 显示的值
    showValue() {
      const selectedItem = this.columns.find(
        item => item === this.value || (typeof item === 'object' && item.value === this.value)
      )
      return selectedItem ? (typeof selectedItem === 'object' ? selectedItem.text : selectedItem) : ''
    }
  },

  watch: {
    // 监听值变化，更新默认选中项
    value: {
      handler(newVal) {
        this.setDefaultIndex(newVal)
      },
      immediate: true
    }
  },

  methods: {
    // 设置默认选中项
    setDefaultIndex(value) {
      try {
        this.defaultIndex = this.columns.findIndex(
          item => item === value || (typeof item === 'object' && item.value === value)
        )
        if (this.defaultIndex === -1) {
          this.defaultIndex = 0
        }
      } catch (error) {
        console.error('设置默认值失败:', error)
        this.defaultIndex = 0
      }
    },

    // 显示选择器
    showPicker() {
      this.isVisible = true
    },

    // 确认选择
    onConfirm(value, index) {
      this.isVisible = false
      const selectedValue = typeof value === 'object' ? value.value : value
      this.$emit('input', selectedValue)
      this.$emit('confirm', value, index)
    },

    // 取消选择
    onCancel() {
      this.isVisible = false
      this.$emit('cancel')
    },

    // 选项改变
    onChange(picker, value, index) {
      this.$emit('change', { picker, value, index })
    }
  }
}
</script>

<style scoped>
.custom-picker {
  width: 100%;
}

.picker-trigger {
  width: 100%;
  cursor: pointer;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.selected-value {
  /*padding: 10px 0;*/
  color: #999999;
  margin-right: 5px;
}

.valueColor {
  color: #323233;
}

.selected-value:empty::before {
  content: attr(placeholder);
  color: #969799;
}
</style>
