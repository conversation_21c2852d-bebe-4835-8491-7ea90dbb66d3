<template>
  <div class="map-container">
    <div id="map" ref="mapContainer"></div>
    <!-- 显示坐标和地址信息 -->
    <div v-if="currentAddress" class="address-info">
      <p>经纬度: {{ currentPosition.lat }}, {{ currentPosition.lng }}</p>
      <p>地址: {{ currentAddress }}</p>
    </div>
    <div style="position: absolute; bottom: 5px; left: 137px; z-index: 9999; display: flex; gap: 10px">
      <van-button type="primary" size="small" @click="location">当前位置</van-button>
      <van-button v-if="type" :disabled="isCanPoint" size="small" type="primary" @click="flag">标记位置</van-button>
      <van-button v-if="!type" :disabled="editMode" size="small" type="primary" @click="bianji">编辑位置</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MapComponent',
  props: {
    coordinates: {
      type: [Array, Object], // 支持数组 [lat, lng] 或对象 {lat, lng}
      default: null
    }
  },
  data() {
    return {
      type: 1, //0父组件传入点位 1父组件未传入点位
      editMode: false, // 新增编辑模式状态
      map: null,
      marker: null,
      locationCircle: null,
      currentPosition: {
        lat: null,
        lng: null,
      },
      isCanPoint: false,
      currentAddress: '',
      tiandituKey: '301de34e264e4a45b3d000d5cff0a870', // 替换成你的天地图密钥
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      // 初始化地图，定位到中国中心，修改初始缩放级别为5（而不是之前的4或11）
      this.map = L.map('map', {
        center: [29.86166, 119.195397],
        zoom: 12,
        zoomControl: true,
        attributionControl: false,
      })

      // 添加天地图影像底图 - 使用https而不是http
      L.tileLayer(
        `https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${this.tiandituKey}`,
        {
          maxZoom: 18,
          tileSize: 256,
          zoomOffset: 0,
        }
      ).addTo(this.map)

      // 添加天地图标注 - 使用https而不是http
      L.tileLayer(
        `https://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${this.tiandituKey}`,
        {
          maxZoom: 18,
          tileSize: 256,
          zoomOffset: 0,
        }
      ).addTo(this.map)


      // 添加点击事件监听
      this.map.on('click', this.handleMapClick)

      // 在地图初始化完成后，如果有初始坐标则添加标记
      if (this.coordinates) {
        this.type = 0
        const lat = Array.isArray(this.coordinates) ? this.coordinates[0] : this.coordinates.lat
        const lng = Array.isArray(this.coordinates) ? this.coordinates[1] : this.coordinates.lng
        this.currentPosition = { lat, lng }
        this.reverseGeocode(lat, lng)
        this.addInitialMarker(lat, lng)
      }
      if (!this.coordinates) {
        this.location()
      }
    },

    handleMapClick(e) {
      if (this.isCanPoint || this.editMode) {
        const { lat, lng } = e.latlng

        // 更新当前位置
        this.currentPosition = { lat, lng }

        // 如果已有标记，则移除
        if (this.marker) {
          this.map.removeLayer(this.marker)
          this.marker = null
        }

        // 添加新标记
        this.marker = L.marker([lat, lng]).addTo(this.map)

        // 进行逆地理编码
        this.reverseGeocode(lat, lng)
      }
      this.isCanPoint = false
      this.editMode = false // 退出编辑模式
    },

    async reverseGeocode(lat, lng) {
      try {
        // 使用https而不是http
        const response = await fetch(
          `https://api.tianditu.gov.cn/geocoder?postStr={'lon':${lng},'lat':${lat},'ver':1}&type=geocode&tk=${this.tiandituKey}`
        )
        const data = await response.json()
        if (data.status === '0') {
          console.log(data)
          this.currentAddress = data.result.formatted_address
          this.$emit('locationSelected', data.result)
        } else {
          this.currentAddress = '获取地址失败'
        }
      } catch (error) {
        console.error('逆地理编码失败:', error)
        this.currentAddress = '获取地址失败'
      }
    },
    addInitialMarker(lat, lng) {
      let this_ = this
      if (this_.marker) {
        this_.map.removeLayer(this_.marker)
      }
      this_.marker = L.marker([lat, lng]).addTo(this_.map)
      this_.map.setView([lat, lng], 12)
    },
    flag() {
      this.isCanPoint = true
    },
    bianji() {
      this.editMode = true // 进入编辑模式
    },
    location() {
      let this_ = this

      // 如果已有标记，则移除
      if (this_.locationCircle) {
        this_.map.removeLayer(this_.locationCircle)
        this_.locationCircle = null
      }

      this_.map.locate({
        setView: true,
        maxZoom: 16,
      })
      this_.map.on('locationfound', function (e) {
        console.log('jjj', e)
        var radius = e.accuracy / 2
        this_.locationCircle = L.circle([e.latlng.lat, e.latlng.lng], { radius: radius }).addTo(this_.map)
      })
      this_.map.on('locationerror', function (e) {
        console.log('定位错误')
      })
    },
  },
}
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

#map {
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.address-info {
  width: 350px;
  position: absolute;
  bottom: 60px;
  left: 23px;
  background: white;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}
</style>
