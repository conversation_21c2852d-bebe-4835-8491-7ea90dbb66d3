<template>
  <div class="clock-in-page">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <van-icon name="arrow-left" />
      </div>
      <div class="title">考勤打卡</div>
      <div class="placeholder"></div>
    </div>

    <!-- 入口卡片列表 -->
    <div class="entry-list">
      <!-- 采集员考勤入口 -->
      <div class="entry-card" @click="goToClockIn('collector')">
        <div class="entry-icon">
          <img src="@/assets/clockIn/采集员考勤.png" alt="采集员考勤" />
        </div>
        <div class="entry-info">
          <div class="entry-title">采集员考勤</div>
          <div class="entry-desc">采集员考勤入口</div>
        </div>
        <div class="entry-arrow">
          <van-icon name="arrow" />
        </div>
      </div>

      <!-- 站前考勤入口 -->
      <div class="entry-card" @click="goToClockIn('station')">
        <div class="entry-icon">
          <img src="@/assets/clockIn/站前考勤.png" alt="站前考勤" />
        </div>
        <div class="entry-info">
          <div class="entry-title">站前考勤</div>
          <div class="entry-desc">站前考勤入口</div>
        </div>
        <div class="entry-arrow">
          <van-icon name="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ClockInEntry",
  data() {
    return {}
  },
  computed: {},
  mounted() {
    // 页面加载时的逻辑
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 跳转到对应的考勤打卡页面
    goToClockIn(type) {
      // 根据类型跳转到不同的考勤页面
      if (type === 'collector') {
        this.$router.push('/CollectorClockIn');
      } else if (type === 'station') {
        this.$router.push('/StationFrontAttendance');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.clock-in-page {
  min-height: 100vh;
  background-color: #f7f8fa;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    height: 44px;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .back-icon {
      font-size: 20px;
      color: #333;
      padding: 4px;
    }

    .title {
      font-size: 16px;
      color: #323233;
      font-weight: 500;
    }

    .placeholder {
      width: 24px;
    }
  }

  .entry-list {
    padding: 16px;

    .entry-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: #fff;
      border-radius: 12px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.98);
      }

      .entry-icon {
        border-radius: 8px;
        overflow: hidden;
        background-color: #f0f9ff;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 42px;
          height: 42px;
        }
      }

      .entry-info {
        flex: 1;
        margin-left: 12px;

        .entry-title {
          font-size: 15px;
          color: #323233;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .entry-desc {
          font-size: 12px;
          color: #969799;
        }
      }

      .entry-arrow {
        color: #c8c9cc;
        font-size: 16px;
      }
    }
  }
}
</style>
