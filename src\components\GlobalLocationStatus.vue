<template>
  <div v-if="showStatus" class="global-location-status">
    <div class="status-bar" @click="toggleDetails">
      <div class="status-icon">
        <van-icon name="location-o" :color="statusColor" size="16" />
        <div class="pulse-dot" :class="{ active: isTracking }"></div>
      </div>
      <div class="status-text">
        {{ statusText }}
      </div>
      <div class="toggle-icon">
        <van-icon :name="showDetails ? 'arrow-up' : 'arrow-down'" size="12" />
      </div>
    </div>
    
    <van-collapse-item v-if="showDetails" class="status-details">
      <div class="detail-item">
        <span class="label">状态:</span>
        <span class="value">{{ isTracking ? '定位追踪中' : '未追踪' }}</span>
      </div>
      <div class="detail-item">
        <span class="label">用户:</span>
        <span class="value">{{ currentUser?.userName || '未知' }}</span>
      </div>
      <div class="detail-item">
        <span class="label">上报间隔:</span>
        <span class="value">5秒</span>
      </div>
      <div class="detail-item">
        <span class="label">最后上报:</span>
        <span class="value">{{ lastReportTime || '暂无' }}</span>
      </div>
      
      <div class="action-buttons">
        <van-button 
          size="mini" 
          type="primary" 
          @click="forceCheck"
        >
          检查状态
        </van-button>
        <van-button 
          size="mini" 
          type="warning" 
          @click="showLogs"
        >
          查看日志
        </van-button>
      </div>
    </van-collapse-item>
  </div>
</template>

<script>
export default {
  name: 'GlobalLocationStatus',
  data() {
    return {
      isTracking: false,
      currentUser: null,
      lastReportTime: null,
      showDetails: false,
      showStatus: false
    }
  },
  computed: {
    statusText() {
      if (this.isTracking) {
        return '定位追踪中'
      }
      return '定位已停止'
    },
    statusColor() {
      return this.isTracking ? '#1989fa' : '#999'
    }
  },
  mounted() {
    // 监听全局定位追踪事件
    window.addEventListener('locationTracking', this.handleLocationEvent)
    
    // 初始化状态
    this.updateStatus()
  },
  beforeDestroy() {
    window.removeEventListener('locationTracking', this.handleLocationEvent)
  },
  methods: {
    handleLocationEvent(event) {
      const { type, data, user } = event.detail
      
      switch (type) {
        case 'started':
          this.isTracking = true
          this.currentUser = user
          this.showStatus = true
          break
        case 'stopped':
          this.isTracking = false
          this.lastReportTime = null
          break
        case 'location_reported':
          this.lastReportTime = new Date().toLocaleTimeString()
          break
      }
    },
    
    updateStatus() {
      if (this.$globalLocationManager) {
        const status = this.$globalLocationManager.getStatus()
        this.isTracking = status.timerStatus.isRunning
        this.currentUser = status.currentUser
        this.showStatus = this.isTracking
      }
    },
    
    toggleDetails() {
      this.showDetails = !this.showDetails
    },
    
    forceCheck() {
      if (this.$globalLocationManager) {
        this.$globalLocationManager.forceCheckClockStatus()
        this.$toast.success('状态检查已触发')
      }
    },
    
    showLogs() {
      // 跳转到测试页面查看日志
      this.$router.push('/TimerTest')
    }
  }
}
</script>

<style lang="scss" scoped>
.global-location-status {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-width: 200px;
  
  .status-bar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    
    .status-icon {
      position: relative;
      margin-right: 8px;
      
      .pulse-dot {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 6px;
        height: 6px;
        background-color: #1989fa;
        border-radius: 50%;
        opacity: 0;
        
        &.active {
          opacity: 1;
          animation: pulse 1.5s infinite;
        }
      }
    }
    
    .status-text {
      flex: 1;
      font-size: 12px;
      color: #333;
    }
    
    .toggle-icon {
      margin-left: 8px;
    }
  }
  
  .status-details {
    border-top: 1px solid #eee;
    padding: 12px;
    
    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 12px;
      
      .label {
        color: #666;
      }
      
      .value {
        color: #333;
        font-weight: 500;
      }
    }
    
    .action-buttons {
      margin-top: 12px;
      display: flex;
      gap: 8px;
      
      .van-button {
        flex: 1;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
