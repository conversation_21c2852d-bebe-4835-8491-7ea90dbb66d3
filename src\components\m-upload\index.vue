<template>
  <div class="m-upload">
    <van-uploader v-model="$attrs.value" v-bind="$attrs" v-on="$listeners">
      <div class="van-uploader__upload" style="position: relative; z-index: 10;" @click="chooseImage">
        <i class="van-icon van-icon-photograph van-uploader__upload-icon" />
      </div>
    </van-uploader>
  </div>
</template>

<script>
import lrz from 'lrz'

export default {
  name: 'MUpload',
  methods: {
    dataURLtoBlobFile: function(dataurl) {
      let arr = dataurl.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let suffix = mime.split('/')[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], `${new Date().getTime()}.${suffix}`, {
        type: mime
      })
    },
    chooseImage() {
      if (window.ZWJSBridge.chooseImage) {
        window.ZWJSBridge.chooseImage({ upload: false }).then(async res => {
          console.log('res.picSrc', res.picSrc)
          if (Array.isArray(res.picSrc)) {
            let fileListPromise = res.picSrc.map(item => {
              return new Promise((resolve, reject) => {
                // 直接使用原始base64数据
                const base64Data = /data:/.test(item) ? item : `data:image/jpeg;base64,${item}`
                
                // 创建一个Image对象来加载图片
                const img = new Image()
                img.src = base64Data
                
                img.onload = () => {
                  // 创建canvas来处理图片
                  const canvas = document.createElement('canvas')
                  const ctx = canvas.getContext('2d')
                  canvas.width = img.width
                  canvas.height = img.height
                  ctx.drawImage(img, 0, 0, img.width, img.height)
                  
                  // 转换为Blob
                  canvas.toBlob((blob) => {
                    const file = new File([blob], `${Date.now()}.jpg`, {
                      type: 'image/jpeg',
                      lastModified: Date.now()
                    })
                    
                    resolve({
                      content: base64Data,
                      file: file,
                      message: '',
                      status: 'success'
                    })
                    this.$emit('fileUpload')
                  }, 'image/jpeg', 0.8) // 0.8是压缩质量
                }
                
                img.onerror = () => {
                  reject({
                    message: '图片加载失败',
                    status: 'error'
                  })
                }
              })
            })
            
            const fileList = await Promise.all(fileListPromise)
            console.log('fileList', fileList)
            this.$emit('input', [...this.$attrs.value, ...fileList])
          } else {
            this.$toast.fail('图片上传失败')
          }
        }).catch(() => {
          this.$toast.fail('图片上传失败')
        })
      } else {
        this.$toast.fail('接口调用失败，请退出重试')
      }
    }
  }
}
</script>
