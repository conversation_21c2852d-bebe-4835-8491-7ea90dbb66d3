# 全局定位追踪架构说明

## 架构概述

全局定位追踪系统采用分层架构设计，实现了在应用级别的位置追踪管理，确保用户在任何页面都能持续上报位置信息。

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                        应用层 (App.vue)                      │
│  ┌─────────────────────┐  ┌─────────────────────────────────┐ │
│  │  GlobalLocationStatus │  │         Router View           │ │
│  │    (状态显示组件)      │  │        (页面组件)             │ │
│  └─────────────────────┘  └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      全局管理层                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            GlobalLocationManager                        │ │
│  │  • 用户状态管理                                          │ │
│  │  • 打卡状态检查                                          │ │
│  │  • 定时器生命周期管理                                     │ │
│  │  • 全局事件分发                                          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      定时器服务层                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                LocationTimer                            │ │
│  │  • 定时器管理                                            │ │
│  │  • 位置获取                                              │ │
│  │  • 地址解析                                              │ │
│  │  • 页面可见性管理                                         │ │
│  │  • 状态持久化                                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      API服务层                              │
│  ┌─────────────────────┐  ┌─────────────────────────────────┐ │
│  │   getCurrentClockStatus │  │      personPosition         │ │
│  │    (获取打卡状态)      │  │      (上报位置信息)          │ │
│  └─────────────────────┘  └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. GlobalLocationManager (全局定位管理器)

**职责**:
- 管理用户登录状态和信息
- 定期检查打卡状态
- 控制定时器的启动和停止
- 发送全局事件通知

**关键方法**:
```javascript
// 初始化管理器
init(userInfo)

// 检查打卡状态
checkClockStatus()

// 启动/停止定位追踪
startLocationTracking()
stopLocationTracking()

// 强制检查状态
forceCheckClockStatus()
```

### 2. LocationTimer (定时器服务)

**职责**:
- 管理定时器生命周期
- 获取当前位置信息
- 执行位置上报
- 处理页面可见性变化
- 状态持久化存储

**关键方法**:
```javascript
// 启动定时器
start(options)

// 停止定时器
stop()

// 获取当前位置
getCurrentPosition()

// 执行全局定位上报
executeGlobalLocationReport()
```

### 3. GlobalLocationStatus (状态显示组件)

**职责**:
- 显示定位追踪状态
- 提供用户交互界面
- 监听全局事件
- 提供快捷操作

**功能特性**:
- 实时状态显示
- 脉冲动画指示器
- 详细信息展开
- 快捷操作按钮

## 数据流

### 1. 初始化流程

```
用户登录 → 获取用户信息 → 初始化GlobalLocationManager → 
检查打卡状态 → 判断是否需要启动定时器 → 启动LocationTimer → 
开始位置上报
```

### 2. 打卡流程

```
用户打卡 → 打卡成功 → 触发状态检查 → GlobalLocationManager检查状态 → 
启动/停止定时器 → 发送全局事件 → 更新UI状态
```

### 3. 位置上报流程

```
定时器触发 → 获取当前位置 → 地址解析 → 调用API上报 → 
发送全局事件 → 更新最后上报时间
```

### 4. 应用重启恢复流程

```
应用启动 → 读取本地存储 → 检查定时器状态 → 调用API验证打卡状态 → 
判断是否需要恢复 → 恢复定时器 → 继续位置上报
```

## 事件系统

### 全局事件类型

```javascript
// 定位追踪启动
{
  type: 'started',
  data: null,
  user: userInfo
}

// 定位追踪停止
{
  type: 'stopped', 
  data: null,
  user: userInfo
}

// 位置上报成功
{
  type: 'location_reported',
  data: locationData,
  user: userInfo
}
```

### 事件监听

```javascript
// 监听全局定位追踪事件
window.addEventListener('locationTracking', (event) => {
  const { type, data, user } = event.detail
  // 处理事件
})
```

## 状态管理

### 1. 本地存储

```javascript
// 定时器状态
localStorage.setItem('locationTimerState', JSON.stringify({
  isRunning: true,
  userId: 'user123',
  userType: '1',
  startTime: '2024-01-15T09:00:00.000Z',
  interval: 5000
}))
```

### 2. 内存状态

```javascript
// 全局管理器状态
{
  isInitialized: true,
  currentUser: {
    userId: 'user123',
    userType: '1',
    userName: '张三'
  },
  timerStatus: {
    isRunning: true,
    startTime: '2024-01-15T09:00:00.000Z'
  }
}
```

## 生命周期管理

### 1. 应用启动

1. 导入全局定位管理器
2. 挂载到Vue原型
3. 用户登录后初始化
4. 检查并恢复定时器状态

### 2. 页面切换

1. 定时器继续运行
2. 状态组件保持显示
3. 事件监听保持活跃
4. 位置上报不中断

### 3. 页面隐藏/显示

1. 页面隐藏时暂停定时器
2. 页面显示时恢复定时器
3. 状态保持不变
4. 节省电池消耗

### 4. 应用关闭

1. 保存定时器状态到本地存储
2. 清理事件监听器
3. 停止定时器
4. 释放资源

## 错误处理

### 1. 网络异常

- 位置上报失败时记录错误日志
- 不影响定时器继续运行
- 下次定时器触发时重试

### 2. 位置获取失败

- 记录错误信息
- 使用上次已知位置
- 或跳过本次上报

### 3. API调用失败

- 记录详细错误信息
- 不停止定时器
- 等待下次重试

### 4. 状态恢复失败

- 清除本地存储
- 重新初始化
- 记录错误日志

## 性能优化

### 1. 内存管理

- 使用单例模式避免重复实例
- 及时清理事件监听器
- 控制日志数量

### 2. 电池优化

- 页面隐藏时暂停定时器
- 使用高效的位置获取策略
- 避免频繁的DOM操作

### 3. 网络优化

- 合理的超时设置
- 失败重试机制
- 避免重复请求

## 扩展性

### 1. 支持多种定位方式

- GPS定位
- 网络定位
- 基站定位

### 2. 支持多种上报策略

- 固定间隔上报
- 位置变化上报
- 智能上报

### 3. 支持多种存储方式

- localStorage
- sessionStorage
- IndexedDB

### 4. 支持多种通知方式

- 全局事件
- Vuex状态管理
- 消息总线

## 安全考虑

### 1. 数据安全

- 位置信息加密传输
- 敏感数据不存储在本地
- API接口鉴权

### 2. 隐私保护

- 用户授权位置权限
- 明确告知位置用途
- 提供关闭选项

### 3. 防止滥用

- 合理的上报频率
- 服务器端限流
- 异常检测机制
