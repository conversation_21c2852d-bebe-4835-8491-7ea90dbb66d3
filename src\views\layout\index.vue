<template>
  <div class="layout-container">
    <div class="content-area" :style="{ minHeight: `calc(100vh - ${showTabbar ? 50 : 0}px)` }">
      <router-view />
    </div>
    <van-tabbar v-if="showTabbar" route active-color="#1989fa" inactive-color="#7d7e80" style="z-index: 9000;">
      <van-tabbar-item icon="home-o" to="/">首页</van-tabbar-item>
      <van-tabbar-item icon="setting-o" to="/personCenter">设置</van-tabbar-item>
    </van-tabbar>
  </div>

</template>

<script>

export default {
  name: 'LayoutIndex',
  components: {},
  props: {},
  data() {
    return {
    }
  },
  computed: {
    showTabbar() {
      const name = this.$route && this.$route.name
      return name === 'Home' || name === 'PersonCenter'
    }
  },
  watch: {
  },
  mounted() {

  },
  created() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.layout-container {
  min-height: 100vh;
  position: relative;
}

.content-area {
  min-height: calc(100vh - 50px); /* 减去tab栏高度 */
  // padding-bottom: 50px; /* 为底部tab栏留出空间 */
  box-sizing: border-box;
}

/* 确保tabbar固定在底部 */
:deep(.van-tabbar) {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
}
</style>
