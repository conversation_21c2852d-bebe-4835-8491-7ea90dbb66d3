/**
 * 全局定位管理器
 * 负责在应用级别管理定位定时器的启动和停止
 */

import locationTimer from './locationTimer'
import { getCurrentClockStatus } from '@/api/clockIn'

class GlobalLocationManager {
  constructor() {
    this.isInitialized = false
    this.currentUser = null
    this.checkInterval = null
    
    // 绑定方法
    this.init = this.init.bind(this)
    this.checkClockStatus = this.checkClockStatus.bind(this)
    this.startLocationTracking = this.startLocationTracking.bind(this)
    this.stopLocationTracking = this.stopLocationTracking.bind(this)
  }

  /**
   * 初始化全局定位管理器
   * @param {Object} userInfo 用户信息
   */
  async init(userInfo) {
    if (this.isInitialized) {
      console.log('全局定位管理器已初始化')
      return
    }

    this.currentUser = userInfo
    this.isInitialized = true

    console.log('初始化全局定位管理器:', userInfo)

    // 立即检查一次打卡状态
    await this.checkClockStatus()

    // 设置定期检查打卡状态（每分钟检查一次）
    this.checkInterval = setInterval(this.checkClockStatus, 60000)

    console.log('全局定位管理器初始化完成')
  }

  /**
   * 检查当前打卡状态
   */
  async checkClockStatus() {
    if (!this.currentUser) {
      console.log('用户信息不存在，跳过打卡状态检查')
      return
    }

    try {
      const response = await getCurrentClockStatus(this.currentUser.userId, this.currentUser.userType)
      
      if (response && response.data) {
        const data = response.data
        const today = new Date().toISOString().split('T')[0]
        
        const clockStatus = {
          hasStartClock: !!data.actualStartTime,
          hasEndClock: !!data.actualEndTime,
          clockDate: data.punchTime ? data.punchTime.split(' ')[0] : today,
          punchId: data.punchId
        }

        console.log('打卡状态检查结果:', clockStatus)

        // 判断是否需要启动或停止定位追踪
        if (this.shouldStartTracking(clockStatus)) {
          this.startLocationTracking()
        } else if (this.shouldStopTracking(clockStatus)) {
          this.stopLocationTracking()
        }
      }
    } catch (error) {
      console.error('检查打卡状态失败:', error)
    }
  }

  /**
   * 判断是否应该启动定位追踪
   */
  shouldStartTracking(clockStatus) {
    const today = new Date().toISOString().split('T')[0]
    const timerStatus = locationTimer.getStatus()
    
    // 如果是今天，已上班打卡但未下班打卡，且定时器未运行，则启动
    return clockStatus.clockDate === today && 
           clockStatus.hasStartClock && 
           !clockStatus.hasEndClock && 
           !timerStatus.isRunning
  }

  /**
   * 判断是否应该停止定位追踪
   */
  shouldStopTracking(clockStatus) {
    const today = new Date().toISOString().split('T')[0]
    const timerStatus = locationTimer.getStatus()
    
    // 如果已下班打卡或不是今天的打卡，且定时器在运行，则停止
    return timerStatus.isRunning && 
           (clockStatus.hasEndClock || clockStatus.clockDate !== today)
  }

  /**
   * 启动定位追踪
   */
  startLocationTracking() {
    if (!this.currentUser) {
      console.log('用户信息不存在，无法启动定位追踪')
      return
    }

    const timerStatus = locationTimer.getStatus()
    if (timerStatus.isRunning) {
      console.log('定位追踪已在运行中')
      return
    }

    locationTimer.start({
      userId: this.currentUser.userId,
      userType: this.currentUser.userType,
      callback: this.locationCallback
    })

    console.log('全局定位追踪已启动')
    
    // 发送全局事件通知
    this.emitLocationTrackingEvent('started')
  }

  /**
   * 停止定位追踪
   */
  stopLocationTracking() {
    const timerStatus = locationTimer.getStatus()
    if (!timerStatus.isRunning) {
      console.log('定位追踪未运行')
      return
    }

    locationTimer.stop()
    console.log('全局定位追踪已停止')
    
    // 发送全局事件通知
    this.emitLocationTrackingEvent('stopped')
  }

  /**
   * 定位回调函数
   */
  locationCallback(data) {
    console.log('全局定位追踪触发:', data)
    
    // 发送全局事件通知
    this.emitLocationTrackingEvent('location_reported', data)
  }

  /**
   * 发送全局事件
   */
  emitLocationTrackingEvent(type, data = null) {
    const event = new CustomEvent('locationTracking', {
      detail: {
        type,
        data,
        timestamp: new Date().toISOString(),
        user: this.currentUser
      }
    })
    
    window.dispatchEvent(event)
  }

  /**
   * 手动触发打卡状态检查
   */
  async forceCheckClockStatus() {
    console.log('手动触发打卡状态检查')
    await this.checkClockStatus()
  }

  /**
   * 更新用户信息
   */
  updateUserInfo(userInfo) {
    this.currentUser = userInfo
    console.log('用户信息已更新:', userInfo)
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      currentUser: this.currentUser,
      timerStatus: locationTimer.getStatus()
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }

    this.stopLocationTracking()
    this.isInitialized = false
    this.currentUser = null

    console.log('全局定位管理器已销毁')
  }
}

// 创建全局单例
const globalLocationManager = new GlobalLocationManager()

export default globalLocationManager
