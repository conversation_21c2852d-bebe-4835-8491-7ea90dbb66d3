<!--已办任务-->
<template>
  <div class="case-file-archives">
    <!-- 头部搜索区域 -->
    <div class="header">
      <div class="back-btn" @click="goBack">
        <van-icon name="arrow-left" size="18" />
      </div>
      <div class="header-title">已办任务</div>
    </div>

    <div class="header">
      <div class="search-box">
        <van-search
            v-model="searchText"
            placeholder="搜索案件名称/编号"
            shape="round"
            background="transparent"
            @search="onSearch"
        />
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <div class="filter-item" @click="showDateRangePicker = true">
        <span>{{ getDateRangeText() }}</span>
        <van-icon name="arrow-down" size="12" color="#999" />
      </div>
      <div class="filter-item" @click="showTypePopup = true">
        <span>{{ selectedType || '任务类型' }}</span>
        <van-icon name="arrow-down" size="12" color="#999" />
      </div>
      <div class="filter-item" @click="showStatusPopup = true">
        <span>{{ selectedStatus || '来源' }}</span>
        <van-icon name="arrow-down" size="12" color="#999" />
      </div>
    </div>

    <!-- 案卷列表 -->
    <div class="case-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
        >
          <div class="case-item" v-for="(item, index) in caseList" :key="index" @click="viewCaseDetail(item)">
            <div class="case-title">
              <div class="title-content">
                <div class="title-icon"></div>
                <span>{{ item.title }}</span>
              </div>
              <span class="status-tag" :class="getStatusClass(item.status)">{{ item.statusText }}</span>
            </div>
            <div class="case-info">
              <div class="info-row">
                <span class="label">任务类型：</span>
                <span class="value">{{ item.type }}</span>
              </div>
              <!--              <div class="info-row">-->
              <!--                <span class="label">来源：</span>-->
              <!--                <span class="value">{{ item.searchType || '未知' }}</span>-->
              <!--              </div>-->
              <div class="info-row">
                <span class="label">创建时间：</span>
                <span class="value">{{ item.createDate }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 日期选择弹窗 -->
    <van-popup v-model="showDateRangePicker" position="bottom" round>
      <div class="date-range-picker">
        <div class="date-picker-header">
          <div class="cancel-btn" @click="showDateRangePicker = false">取消</div>
          <div class="title">选择日期范围</div>
          <div class="confirm-btn" @click="onDateRangeConfirm">确定</div>
        </div>
        <div class="date-fields">
          <div class="date-field" @click="showStartDatePicker = true">
            <div class="field-label">开始日期</div>
            <div class="field-value">{{ startDate || '请选择' }}</div>
          </div>
          <div class="date-separator">至</div>
          <div class="date-field" @click="showEndDatePicker = true">
            <div class="field-label">结束日期</div>
            <div class="field-value">{{ endDate || '请选择' }}</div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 开始日期选择 -->
    <van-popup v-model="showStartDatePicker" position="bottom">
      <van-datetime-picker
          type="date"
          title="选择开始日期"
          :min-date="new Date(2020, 0, 1)"
          :max-date="new Date()"
          @confirm="onStartDateConfirm"
          @cancel="showStartDatePicker = false"
      />
    </van-popup>

    <!-- 结束日期选择 -->
    <van-popup v-model="showEndDatePicker" position="bottom">
      <van-datetime-picker
          type="date"
          title="选择结束日期"
          :min-date="startDateObj || new Date(2020, 0, 1)"
          :max-date="new Date()"
          @confirm="onEndDateConfirm"
          @cancel="showEndDatePicker = false"
      />
    </van-popup>

    <!-- 任务类型弹窗 -->
    <van-popup v-model="showTypePopup" position="bottom">
      <van-picker
          show-toolbar
          title="选择任务类型"
          :columns="typeColumns"
          @confirm="onTypeConfirm"
          @cancel="showTypePopup = false"
      />
    </van-popup>

    <!-- 来源弹窗 -->
    <van-popup v-model="showStatusPopup" position="bottom">
      <van-picker
          show-toolbar
          title="选择来源"
          :columns="statusColumns"
          @confirm="onStatusConfirm"
          @cancel="showStatusPopup = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { getCaseList } from "@/api/common";

export default {
  name: "CaseFileArchives",
  data() {
    return {
      searchText: '',
      selectedDate: '',
      selectedType: '案件',
      selectedStatus: '',
      showDatePicker: false,
      showTypePopup: false,
      showStatusPopup: false,
      refreshing: false,
      loading: false,
      finished: false,
      caseList: [],
      typeColumns: ['案件','牛皮藓','大综合一体化案件', '专项整治',  '四位一体', '监控抓拍', '智能抓拍',  '黄牛处置', '日常巡查', '违规处置', '一般案件'],
      statusColumns: ['全部', '上报', '处置'],
      titleIcon: require('@/assets/CaseFileArchives/title-icon.png'),
      showDateRangePicker: false,
      showStartDatePicker: false,
      showEndDatePicker: false,
      startDate: '',
      endDate: '',
      startDateObj: null,
      // 任务类型映射表
      typeMap: {
        '案件': '1',
        '专项整治': '2',
        '牛皮藓': '3',
        '四位一体': '4',
        '监控抓拍': '5',
        '智能抓拍': '6',
        '大综合一体化案件': '7',
        '黄牛处置': '8',
        '日常巡查': '9',
        '违规处置': '10',
        '一般案件': '11'
      },
      // 来源映射表
      statusMap: {
        '全部': '',
        '上报': '1',
        '处置': '2'
      },
      page: 1,
      limit: 10,
      total: 0
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      // 初始化数据，加载第一页
      this.page = 1;
      this.caseList = [];
      this.loadCaseList();
    },

    // 加载案卷列表数据
    async loadCaseList() {
      try {
        this.loading = true;

        // 构建查询参数
        const params = {
          pageNum: this.page,
          pageSize: this.limit
        };

        // 添加类型过滤
        if (this.selectedType && this.selectedType !== '全部') {
          params.type = this.typeMap[this.selectedType];
        }

        // 添加来源过滤
        if (this.selectedStatus && this.selectedStatus !== '全部') {
          params.searchType = this.statusMap[this.selectedStatus];
        }

        // 添加日期范围
        if (this.startDate) {
          params.startTime = `${this.startDate}`;
        }
        if (this.endDate) {
          params.endTime = `${this.endDate}`;
        }

        // 添加搜索关键词
        if (this.searchText) {
          params.keyWord = this.searchText;
        }

        const res = await getCaseList(params);

        if (res.code === 200) {
          const caseData = res.rows || [];

          // 处理返回的数据，转换为组件需要的格式
          const formattedData = caseData.map(item => ({
            title: item.content || '未命名案件',
            createDate: item.createTime || '',
            status: item.status !== undefined ? Number(item.status) : 0,
            statusText: this.getStatusText(item.status),
            id: item.eventId || '',
            type: this.getTypeName(item.type) || '未分类',
            searchType: this.getLaiyuanText(item.searchType) || '未知',
          }));

          // 追加数据到列表
          if (this.page === 1) {
            this.caseList = formattedData;
          } else {
            this.caseList = [...this.caseList, ...formattedData];
          }

          // 更新总数
          this.total = res.total || 0;

          // 判断是否已加载完全部数据
          this.finished = this.caseList.length >= this.total;
          this.page++;
        } else {
          this.$toast.fail(res.msg || '获取案卷列表失败');
          this.finished = true;
        }
      } catch (error) {
        console.error('获取案卷列表异常:', error);
        this.$toast.fail('获取案卷列表失败，请稍后重试');
        this.finished = true;
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    // 根据状态获取状态文本
    getStatusText(status) {
      const statusTextMap = {
        1:'上报',
        2:'立案',
        3:'派遣',
        4:'处置',
        5:'核查',
        6:'结案',
      };
      return statusTextMap[status] || '未知状态';
    },

    // 根据来源ID获取来源文本
    getLaiyuanText(searchTypeId) {
      // 反向查找来源映射表
      for (const [key, value] of Object.entries(this.statusMap)) {
        if (value === searchTypeId) {
          return key;
        }
      }
      return '未知来源';
    },

    // 根据类型ID获取类型名称
    getTypeName(typeId) {
      // 反向查找类型映射表
      for (const [key, value] of Object.entries(this.typeMap)) {
        if (value == typeId) {
          return key;
        }
      }
      return '未知类型';
    },

    goBack() {
      this.$router.go(-1);
    },

    onSearch() {
      // 实现搜索功能
      this.resetList();
    },

    resetList() {
      console.log(this.startDate, this.endDate);
      this.page = 1;
      this.finished = false;
      this.caseList = [];
      this.loadCaseList();
    },

    onRefresh() {
      // 下拉刷新
      this.page = 1;
      this.finished = false;
      this.loadCaseList();
    },

    onLoad() {
      // 加载更多数据
      if (!this.loading && !this.finished) {
        this.loadCaseList();
      }
    },

    viewCaseDetail(item) {
      // 查看案件详情
      switch (item.type) {
        case '案件':
          this.$router.push(`/ToDoTasksDetail?id=${item.id}`);
          break;
        case '专项整治':
          if (parseInt(item.isShow) === 1) {
            // 整治详情
            this.$router.push({
              name: 'SpecialRectificationSubmitZz',
              query: {
                detailId: item.id
              }
            });
          } else if (parseInt(item.isShow) === 2) {
            // 普查详情
            this.$router.push({
              name: 'SpecialRectificationSubmit',
              query: {
                detailId: item.id
              }
            });
          }
          break;
        case '牛皮藓':
          this.$router.push(`/PsoriasisDetail?id=${item.id}`);
          break;
        case '四位一体':
          this.$router.push(`/FourInOne?id=${item.id}`);
          break;
        case '监控抓拍':
          this.$router.push(`/Snap?id=${item.id}`);
          break;
        case '智能抓拍':
          this.$router.push(`/Snap?id=${item.id}`);
          break;
        case '大综合一体化案件':
          // this.$router.push(`/wuweiDetail/?id=${item.id}`);
          break;
        case '黄牛处置':
          this.$router.push(`/YellowCattleDisposal?id=${item.id}`);
          break;
        case '日常巡查':
          this.$router.push(`/DailyPatrol?id=${item.id}`);
          break;
        case '违规处置':
          this.$router.push(`/ViolationDisposal?id=${item.id}`);
          break;
        case '一般案件':
          this.$router.push(`/GeneralCases?id=${item.id}`);
          break;
      }
    },

    getStatusClass(status) {
      // 根据状态返回不同的样式类
      const statusMap = {
        0: 'status-pending',
        1: 'status-processing',
        2: 'status-processed'
      };
      return statusMap[status] || 'status-pending';
    },

    onDateConfirm(date) {
      this.selectedDate = this.formatDate(date);
      this.showDatePicker = false;
      this.resetList();
    },

    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    onTypeConfirm(value) {
      this.selectedType = value;
      this.showTypePopup = false;
      this.resetList();
    },

    onStatusConfirm(value) {
      this.selectedStatus = value;
      this.showStatusPopup = false;
      this.resetList();
    },

    getDateRangeText() {
      if (this.startDate && this.endDate) {
        return `${this.startDate} 至 ${this.endDate}`;
      } else if (this.startDate) {
        return `开始日期: ${this.startDate}`;
      } else if (this.endDate) {
        return `结束日期: ${this.endDate}`;
      } else {
        return '选择日期范围';
      }
    },

    onDateRangeConfirm() {
      this.showDateRangePicker = false;
      this.resetList();
    },

    onStartDateConfirm(date) {
      this.startDate = this.formatDate(date);
      this.showStartDatePicker = false;
      this.startDateObj = date;
      this.resetList();
    },

    onEndDateConfirm(date) {
      this.endDate = this.formatDate(date);
      this.showEndDatePicker = false;
      this.resetList();
    }
  }
}
</script>

<style lang="scss" scoped>
.case-file-archives {
  min-height: 100vh;
  background-color: #f5f5f5;

  .header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #fff;

    .back-btn {
      padding: 5px;
      margin-right: 5px;
    }
    .header-title {
      flex: 0.9;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .search-box {
      flex: 1;
      background: #fff;
      ::v-deep .van-search {
        padding: 0;

        .van-search__content {
          background-color: #f7f8fa;
          height: 34px;
        }
      }
    }
  }

  .filter-bar {
    display: flex;
    padding: 10px 0;
    background-color: #fff;
    border-bottom: 1px solid #f2f2f2;

    .filter-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 13px;
      color: #333;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 16px;
        width: 1px;
        background-color: #eee;
      }

      span {
        margin-right: 5px;
      }
    }
  }

  .case-list {
    padding-top: 8px;

    .case-item {
      padding: 15px;
      background-color: #fff;
      margin-bottom: 8px;

      .case-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-content {
          display: flex;
          align-items: center;

          .title-icon {
            width: 30px;
            height: 30px;
            margin-right: 6px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            background-image: url('~@/assets/CaseFileArchives/title-icon.png');

            &.type-security {
              background-color: #f56c6c;
              border-radius: 4px;
            }

            &.type-order {
              background-color: #e6a23c;
              border-radius: 4px;
            }

            &.type-traffic {
              background-color: #409eff;
              border-radius: 4px;
            }

            &.type-default {
              background-color: #67c23a;
              border-radius: 4px;
            }
          }
        }

        .status-tag {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;
          font-weight: normal;

          &.status-pending {
            color: #1890ff;
            background-color: #e6f7ff;
          }

          &.status-processing {
            color: #faad14;
            background-color: #fffbe6;
          }

          &.status-processed {
            color: #52c41a;
            background-color: #f6ffed;
          }
        }
      }

      .case-info {
        .info-row {
          margin-bottom: 8px;
          font-size: 13px;
          color: #333;

          .label {
            color: #999;
          }
        }
      }

      .case-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 8px;
        margin-top: 8px;
        border-top: 1px solid #f5f5f5;

        .location {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;

          span {
            margin-left: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 220px;
          }
        }

        .action-btn {
          font-size: 12px;
          color: #1989fa;
        }
      }
    }
  }

  .date-range-picker {
    background-color: #fff;

    .date-picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #eee;

      .cancel-btn {
        color: #999;
        font-size: 14px;
      }

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .confirm-btn {
        color: #1989fa;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .date-fields {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 15px;

      .date-field {
        flex: 1;

        .field-label {
          font-size: 12px;
          color: #999;
          margin-bottom: 5px;
          text-align: center;
        }

        .field-value {
          font-size: 15px;
          color: #333;
          height: 30px;
          line-height: 30px;
          text-align: center;
        }
      }

      .date-separator {
        padding: 0 10px;
        color: #999;
      }
    }
  }
}
</style>
