import request from '@/util/request'

// 获取上下班规定时间  返回字段: workStartTime上班时间 workEndTime下班时间
export function getClockTime() {
  return request({
    url: '/collectors/late/select',
    method: 'get',
  })
}

// 签到或签退  请求字段
//   "nowLongitude": "2",  经度
//   "nowLatitude": "2",  纬度
//   //1为签到 2为签退 3为上报
//   "status": 2,
//   "remark":"asdasd", 备注信息
//   "filePath":"adsasd"  图片信息
export function signOrOut(data) {
  return request({
    url: '/collectors/attendance/signOrOut',
    method: 'post',
    data
  })
}

//当前打卡信息查询 传:userId    取值this.user.userId
export function getClockInfo(params) {
  return request({
    url: '/collectors/attendance/select',
    method: 'get',
    params
  })
}

//站前打卡信息查询 机关人员打卡详情
export function getOfficeTodayInfo(id) {
  return request({
    url: `/business/punchIn/getOfficeTodayInfo/${id}`,
    method: 'get'
  })
}

//站前打卡信息查询 网格人员打卡详情
export function getGridTodayInfo(id) {
  return request({
    url: `/business/punchIn/getGridTodayInfo/${id}`,
    method: 'get'
  })
}

//站前新增打卡  参数:startLongitude,startLatitude,startAddr,punchTime
export function addPunchIn(data) {
  return request({
    url: `/business/punchIn/add`,
    method: 'post',
    data
  })
}

//站前修改打卡状态 用于下班打卡  参数:endLongitude,endLatitude,endAddr,punchId
export function editPunchIn(data) {
  return request({
    url: `/business/punchIn/edit`,
    method: 'post',
    data
  })
}

// 获取考勤历史记录 参数:userId,type,pageNum,pageSize
export function getAttendanceHistory(params) {
  return request({
    url: '/business/punchIn/history',
    method: 'get',
    params
  })
}

// 获取当前打卡状态 用于判断是否需要启动定时器
export function getCurrentClockStatus(userId, userType) {
  const api = userType == "0" ? getOfficeTodayInfo : getGridTodayInfo
  return api(userId)
}


