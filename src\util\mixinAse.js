import { AESDecrypt, AESEncrypt } from '@/util/aesutil'

// 脱敏中间字
// const nameFn = value => {
//   if (!value) return
//   let res =  AESDecrypt(value, 'ivqpsFQwQqxYUr7f')
//   if (res && res.length == 2) {
//     res = `${res.slice(0, 1)}*`
//   } else if (res && res.length > 2) {
//     let xn = ''
//     for (let i = 0;i < res.length - 2; i++) {
//       xn += '*'
//     }
//     res = `${res.slice(0, 1)}${xn}${res.slice(-1)}`
//   } else if (value && value.length == 2) {
//     res = `${value.slice(0, 1)}*`
//   } else {
//     let xn = ''
//     for (let i = 0;i < value.length - 2; i++) {
//       xn += '*'
//     }
//     res = `${value.slice(0, 1)}${xn}${value.slice(-1)}`
//   }
//   return res || value
// }

const nameFn = value => {
  if (!value) return
  let res = AESDecrypt(value, 'ivqpsFQwQqxYUr7f')
  if (res) {
    res = `*${res.slice(1)}`
  } else {
    value = `*${value.slice(1)}`
  }
  return res || value
}

const phoneFn = value => {
  if (!value) return
  try {
    let res = AESDecrypt(value, 'ivqpsFQwQqxYUr7f')
    if (res) {
      res = `${res.slice(0, 3)}****${res.slice(-4)}`
    } else {
      res = `${value.slice(0, 3)}****${value.slice(-4)}`
    }
    return res || value
  } catch (error) {
    return `${value.slice(0, 3)}****${value.slice(-4)}`
  }
}
/* 加密 */
const encrypt = value => {
  if (!value) return
  return AESEncrypt(value, 'ivqpsFQwQqxYUr7f')
}
/* 解密 */
const decrypt = value => {
  if (!value) return
  return AESDecrypt(value, 'ivqpsFQwQqxYUr7f')
}

const mixinAse = {
  filters: {
    name: nameFn,
    phone: phoneFn
  },
  methods: {
    aseName: nameFn,
    asePhone: phoneFn,
    encrypt: encrypt,
    decrypt: decrypt
  }
}

export default mixinAse
