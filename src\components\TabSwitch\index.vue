<template>
  <div class="tab-switch">
    <van-tabs
      v-model="modelValue"
      :border="false"
      :line-width="20"
      line-height="2px"
      color="#1989fa"
      title-active-color="#1989fa"
      title-inactive-color="#666"
      @change="handleChange"
    >
      <van-tab
        v-for="(item, index) in tabs"
        :key="index"
        :title="item.label"
        :name="item.value"
      >
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
export default {
  name: 'TabSwitch',
  props: {
    // 标签数据
    tabs: {
      type: Array,
      default: () => []
    },
    // 当前选中的值
    value: {
      type: [String, Number],
      default: ''
    }
  },
  computed: {
    modelValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.tab-switch {
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  overflow: hidden;

  ::v-deep .van-tabs {
    .van-tabs__wrap {
      padding: 0;

      &::after {
        display: none;
      }
    }

    .van-tab {
      font-size: 15px;
      padding: 15px 0;

      &--active {
        font-weight: 500;
      }
    }

    .van-tabs__line {
      width: 55px !important;
      left: 0;
      height: 2.2px !important;
      bottom: 15px;
      z-index: 99;
    }
  }
}
</style>
