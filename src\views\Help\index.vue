<template>
  <div class="help-container">
    <!-- 头部 -->
    <van-nav-bar
      title="帮助中心"
      left-text=""
      left-arrow
      @click-left="$router.go(-1)"
    />

    <!-- 搜索框 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索帮助文档"
        @search="handleSearch"
        @clear="handleClear"
      />
    </div>

    <!-- 文档列表 -->
    <div class="document-list">
      <van-loading v-if="loading" class="loading-center">加载中...</van-loading>

      <div v-else>
        <div
          v-for="(doc, index) in filteredDocuments"
          :key="index"
          class="document-item"
          @click="openDocument(doc)"
        >
          <div class="doc-icon">
            <van-icon
              :name="doc.type === 'docx' ? 'notes-o' : 'description'"
              size="24"
              :color="doc.type === 'docx' ? '#1890ff' : '#428ffc'"
            />
          </div>
          <div class="doc-info">
            <div class="doc-title">{{ doc.title }}</div>
            <div class="doc-desc">{{ doc.description }}</div>
            <div class="doc-meta">
              <span class="doc-size">{{ doc.size }}</span>
              <span class="doc-date">{{ doc.updateTime }}</span>
            </div>
          </div>
          <div class="doc-arrow">
            <van-icon name="arrow" color="#c8c9cc" />
          </div>
        </div>

        <!-- 空状态 -->
        <van-empty
          v-if="filteredDocuments.length === 0 && !loading"
          description="暂无相关文档"
          image="search"
        />
      </div>
    </div>

<!--    &lt;!&ndash; 快速操作 &ndash;&gt;-->
<!--    <div class="quick-actions" v-if="!searchKeyword && filteredDocuments.length > 0">-->
<!--      <van-grid :column-num="2" :border="false">-->
<!--        <van-grid-item-->
<!--          icon="question-o"-->
<!--          text="常见问题"-->
<!--          @click="openQuickDoc('常见问题')"-->
<!--        />-->
<!--        <van-grid-item-->
<!--          icon="guide-o"-->
<!--          text="操作指南"-->
<!--          @click="openQuickDoc('操作指南')"-->
<!--        />-->
<!--      </van-grid>-->
<!--    </div>-->

<!--    &lt;!&ndash; 底部提示 &ndash;&gt;-->
<!--    <div class="help-footer">-->
<!--      <div class="footer-text">-->
<!--        如需更多帮助，请联系技术支持-->
<!--      </div>-->
<!--      <div class="contact-info">-->
<!--        <van-button-->
<!--          type="primary"-->
<!--          size="small"-->
<!--          @click="contactSupport"-->
<!--        >-->
<!--          联系客服-->
<!--        </van-button>-->
<!--      </div>-->

<!--      &lt;!&ndash; 使用统计 &ndash;&gt;-->
<!--      <div class="usage-stats">-->
<!--        <van-notice-bar-->
<!--          :text="`今日已有 ${todayViews} 人次查看帮助文档`"-->
<!--          left-icon="eye-o"-->
<!--          :scrollable="false"-->
<!--        />-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<script>
import { getDocumentList } from '@/api/help'

export default {
  name: 'Help',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      todayViews: 156, // 模拟今日查看次数
      documents: [
        {
          id: 1,
          title: '三色预警操作手册',
          description: '金华市城市运行管理服务系统三色预警功能详细操作指南',
          filename: '金华市城市运行管理服务系统项目操作手册-三色预警.docx',
          size: '2.5MB',
          updateTime: '2024-01-10',
          type: 'docx'
        },
        {
          id: 2,
          title: '犬类管理操作手册',
          description: '犬类管理模块的详细操作说明和流程指导',
          filename: '金华市城市运行管理服务系统项目操作手册-犬类管理.docx',
          size: '1.8MB',
          updateTime: '2024-01-10',
          type: 'docx'
        },
        {
          id: 3,
          title: '监督指挥操作手册',
          description: '监督指挥功能模块的使用说明和操作流程',
          filename: '金华市城市运行管理服务系统项目操作手册-监督指挥.docx',
          size: '2.1MB',
          updateTime: '2024-01-10',
          type: 'docx'
        },
        {
          id: 4,
          title: '站前管理PC端操作手册',
          description: '站前管理PC端系统的详细操作指南',
          filename: '金华市城市运行管理服务系统项目操作手册-站前管理PC端.docx',
          size: '3.2MB',
          updateTime: '2024-01-10',
          type: 'docx'
        },
        {
          id: 5,
          title: '站前管理移动端操作手册',
          description: '站前管理移动端应用的使用说明和操作指导',
          filename: '金华市城市运行管理服务系统项目操作手册-站前管理移动端.docx',
          size: '2.8MB',
          updateTime: '2024-01-10',
          type: 'docx'
        }
      ]
    }
  },
  computed: {
    filteredDocuments() {
      if (!this.searchKeyword) {
        return this.documents
      }
      return this.documents.filter(doc =>
        doc.title.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        doc.description.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  mounted() {
    this.loadDocuments()
  },
  methods: {
    async loadDocuments() {
      this.loading = true
      try {
        // 这里可以调用API获取文档列表
        // const response = await getDocumentList()
        // this.documents = response.data

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.error('加载文档列表失败:', error)
        this.$toast.fail('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      // 搜索逻辑已在computed中处理
      console.log('搜索关键词:', this.searchKeyword)
    },

    handleClear() {
      this.searchKeyword = ''
    },

    openDocument(doc) {
      // 跳转到文档预览页面
      this.$router.push({
        name: 'DocumentPreview',
        params: {
          id: doc.id,
          filename: doc.filename
        },
        query: {
          title: doc.title,
          description: doc.description,
          size: doc.size,
          updateTime: doc.updateTime
        }
      })
    },

    contactSupport() {
      this.$dialog.alert({
        title: '联系客服',
        message: '技术支持热线：400-xxx-xxxx\n邮箱：<EMAIL>\n工作时间：周一至周五 9:00-18:00'
      })
    },

    openQuickDoc(keyword) {
      // 快速打开特定类型的文档
      const doc = this.documents.find(d => d.title.includes(keyword))
      if (doc) {
        this.openDocument(doc)
      } else {
        this.$toast.fail('暂无相关文档')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.help-container {
  min-height: 100vh;
  background: #f5f5f5;

  .search-section {
    padding: 16px;
    background: #fff;
    margin-bottom: 8px;
  }

  .document-list {
    background: #fff;
    margin-bottom: 16px;

    .loading-center {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .document-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .doc-icon {
        margin-right: 12px;
        flex-shrink: 0;
      }

      .doc-info {
        flex: 1;
        min-width: 0;

        .doc-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }

        .doc-desc {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
          line-height: 1.4;
        }

        .doc-meta {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;

          .doc-size {
            margin-right: 16px;
          }
        }
      }

      .doc-arrow {
        margin-left: 8px;
        flex-shrink: 0;
      }
    }
  }

  .quick-actions {
    background: #fff;
    margin-bottom: 8px;
    padding: 16px 0;

    .van-grid-item {
      .van-grid-item__content {
        padding: 16px 8px;
        background: #f8f9fa;
        border-radius: 8px;
        margin: 0 8px;

        .van-grid-item__icon {
          color: #428ffc;
          margin-bottom: 8px;
        }

        .van-grid-item__text {
          color: #333;
          font-size: 14px;
        }
      }
    }
  }

  .help-footer {
    background: #fff;
    padding: 20px 16px;
    text-align: center;

    .footer-text {
      font-size: 14px;
      color: #666;
      margin-bottom: 12px;
    }

    .contact-info {
      margin-bottom: 16px;

      .van-button {
        min-width: 120px;
      }
    }

    .usage-stats {
      .van-notice-bar {
        background: #f0f9ff;
        border: 1px solid #e1f5fe;
        border-radius: 6px;
      }
    }
  }
}
</style>
