import request from '@/util/request'

export function getCaseDetail(id) {
  return request({
    url: `/police/case/one/${id}`,
    method: 'get',
  })
}

export function caseEdit(data) {
  return request({
    url: `/police/case/one/edit`,
    method: 'post',
    data
  })
}

export function caseRevoke(data) {
  return request({
    url: `/police/case/one/revoke`,
    method: 'post',
    data
  })
}
