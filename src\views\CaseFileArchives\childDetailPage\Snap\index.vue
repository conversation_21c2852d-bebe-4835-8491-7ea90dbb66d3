<template>
	<div class="snap-page">
		<van-nav-bar
			title="抓拍详情"
			left-arrow
			fixed
			@click-left="$router.go(-1)"
			class="custom-nav"
		/>
		<van-form class="container">
			<div class="form-section">
				<!-- <van-field
					v-model="form.title"
					label="案件名称:"
					readonly
					required
					placeholder="请输入案件名称"
					class="form-field"
				/> -->
				<van-field
					v-model="form.userName"
					label="上报人:"
					placeholder="请输入上报人"
					class="form-field"
				/>
				<!-- <van-field
					v-model="form.noticeNo"
					label="抄告单号:"
					required
					placeholder="请输入抄告单号"
					class="form-field"
				/> -->
				<!-- <van-field
					v-model="form.carType"
					label="号牌种类:"
					required
					placeholder="请输入号牌种类"
					class="form-field"
				/> -->
				<van-field
					v-model="form.carNo"
					label="车牌号码:"
					required
					placeholder="请输入车牌号码"
					class="form-field"
				/>
				<van-field
					v-model="form.happenTime"
					label="违规时间:"
					required
					placeholder="请选择违规时间"
					class="form-field"
				/>
				<!-- <van-field
					v-model="form.roadCode"
					label="道路编码:"
					required
					placeholder="请选择道路编码"
					class="form-field"
				/> -->
				<!-- <van-field
					v-model="form.intersection"
					label="路口:"
					required
					placeholder="请选择路口"
					class="form-field"
				/> -->
				<!-- <van-field
					v-model="form.type"
					label="违规类型:"
					required
					placeholder="请选择违规类型"
					class="form-field"
				/> -->
				<van-field
					v-model="form.caseAddress"
					label="违规地点:"
					required
					placeholder="请选择地址描述"
					class="form-field"
				/>
				<!-- <van-field
					v-model="form.behavior"
					label="违法行为:"
					required
					placeholder="请选择违法行为"
					class="form-field"
				/> -->
			</div>
			<!-- 间隔 -->
			<div class="gap"></div>
			<div class="form-section">
				<div class="form-item textarea-item">
					<div class="form-item-label">违法行为:</div>
					<div class="form-item-content">
						<van-field
							v-model="form.behavior"
							type="textarea"
							rows="5"
							maxlength="500"
							readonly
							placeholder="请输入内容描述..."
							class="content-textarea"
						/>
					</div>
				</div>
				<div class="upload-container">
					<div v-for="(file, index) in happenFile" :key="index" class="upload-item">
						<van-image
							width="100px"
							height="100px"
							fit="cover"
							:src="file.url"
							@click="previewImage(happenFile, index)"
							class="case-image"
						/>
					</div>
				</div>
			</div>
			<!-- 间隔 -->
			<!-- <div class="gap"></div>
			<div class="form-section">
				<van-field
					v-model="form.carColor"
					label="车身颜色:"
					readonly
					required
					placeholder="请输入车身颜色"
					class="form-field"
				/>
				<van-field
					v-model="form.carBrand"
					label="车辆品牌:"
					required
					placeholder="请输入车辆品牌"
					class="form-field"
				/>
				<van-field
					v-model="form.carOwnner"
					label="所有人:"
					required
					placeholder="请输入所有人"
					class="form-field"
				/>
				<van-field
					v-model="form.carModel"
					label="车辆车型:"
					required
					placeholder="请输入车辆车型"
					class="form-field"
				/>
				<van-field
					v-model="form.useProperties"
					label="使用性质:"
					required
					placeholder="请输入使用性质"
					class="form-field"
				/>
				<van-field
					v-model="form.ownnerAddress"
					label="住所地址:"
					required
					placeholder="请输入住所地址"
					class="form-field"
				/>
				<van-field
					v-model="form.postalcode"
					label="邮政编码:"
					required
					placeholder="请输入邮政编码"
					class="form-field"
				/>
				<van-field
					v-model="form.phone"
					label="联系电话:"
					required
					placeholder="请输入联系电话"
					class="form-field"
				/>
			</div> -->
		</van-form>
		<!-- 提示 -->
		<van-toast id="van-toast" />
	</div>
</template>

<script>
import { Toast } from 'vant';
import { gettraffic } from "@/api/Snap";
import { getFileList } from "@/api/common";

export default {
	data() {
		return {
			happenFile:[],
			form:{},
		}
	},
	mounted() {
		this.init(this.$route.query);
	},
	methods: {
		async init(params) {
			Toast.loading({
				message: '加载中...',
				forbidClick: true,
			});

			try {
				const res = await gettraffic(params.id);
				this.form = {...res.data};

				const fileRes = await getFileList({ tableName: 'case_traffic', businessId: params.id });
				this.happenFile = fileRes.rows.map(item => {
					return { url: `${process.env.VUE_APP_BASE_API}${item.filePath}?id=${item.fileId}` }
				});

				Toast.clear();
			} catch (error) {
				console.error(error);
				Toast.clear();
				Toast.fail('加载失败');
			}
		},
    // 预览图片
    previewImage(fileList, index) {
      const images = fileList.map(item => item.url);
      this.$imagePreview({
        images,
        startPosition: index,
        closeable: true
      });
    }
	}
}
</script>

<style lang="scss" scoped>
.snap-page {
	padding-top: 46px;
	min-height: 100vh;
	background-color: #f8f8f8;

	.custom-nav {
		background-color: #fff;
		height: 44px;

		:deep(.van-nav-bar__title) {
			font-size: 16px;
			font-weight: 400;
			color: #000;
		}

		:deep(.van-icon) {
			color: #000;
		}
	}

	.container {
		background-color: #f8f8f8;
		padding: 0;
	}

	.form-section {
		padding: 0;
		background-color: #fff;
		margin-bottom: 8px;
	}

	.form-field {
		padding: 10px 16px;
		margin: 0;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 16px;
			right: 16px;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
			transform: scaleY(0.5);
		}

		:deep(.van-field__label) {
			width: 110px;
			color: #000;
			font-size: 14px;
		}

		:deep(.van-cell__value) {
			flex: 1;
			text-align: right;
			overflow: visible;
			display: flex;
			justify-content: flex-end;
			align-items: center;
		}

		:deep(.van-field__control) {
			color: #576B95;
			text-align: right;
			font-size: 14px;
		}
	}

	.form-item {
		display: flex;
		padding: 10px 16px;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 16px;
			right: 16px;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
			transform: scaleY(0.5);
		}

		.form-item-label {
			color: #000;
			font-size: 14px;
			margin-bottom: 8px;
		}
	}

	.textarea-item {
		flex-direction: column;
		align-items: flex-start;

		.form-item-content {
			width: 100%;
		}

		.content-textarea {
			padding: 0;
			background-color: #fff;

			:deep(.van-field__control) {
				text-align: left;
				color: #333;
				min-height: 140px;
			}
		}
	}

	.gap {
		height: 10px;
		background-color: #f8f8f8;
	}

	.upload-container {
		display: flex;
		flex-wrap: wrap;
		padding: 10px 16px;

		.upload-item {
			margin-right: 10px;
			margin-bottom: 10px;
		}

		.case-image {
			border-radius: 4px;
			overflow: hidden;
		}
	}
}
</style>
