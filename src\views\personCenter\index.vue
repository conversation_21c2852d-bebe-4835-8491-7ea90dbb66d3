<template>
  <div class="container">
    <!-- 头部区域 -->
    <div class="container-head">
      <div class="head_box">
        <img src="@/assets/personCenter/head_img.png" alt="" />
      </div>
      <div class="name">{{name}}</div>
      <div class="des">{{desc}}</div>
    </div>
    <div class="list_box">
      <div class="list_item" v-for="(item, index) in listData" :key="index" @click="navClick(item)">
        <div class="item_left">
          <img :src="item.icon" alt="" />
          <div class="title">{{ item.name }}</div>
        </div>
        <van-icon name="arrow" color="#9CA3AF" />
      </div>
    </div>
    <div class="btn" @click="handleLogout">退出登录</div>
  </div>
</template>

<script>
import {removeToken} from "@/util/auth";

export default {
  name: "Home",
  components: {},
  props: {},
  data() {
    return {
      name:"",
      desc:"",
      listData: [
        {
          id: "1",
          name: "数据同步",
          icon: require("@/assets/personCenter/数据同步.png"),
          action: "syncData"
        },
        {
          id: "2",
          name: "清除缓存",
          icon: require("@/assets/personCenter/清除缓存.png"),
          action: "clearCache"
        },
        {
          id: "3",
          name: "惯用语设置",
          icon: require("@/assets/personCenter/惯用语设置.png"),
          url: "/IdiomaticExpressions",
        },
        {
          id: "4",
          name: "今日统计",
          icon: require("@/assets/personCenter/今日统计.png"),
          url: "/TodayStatistics"
        },
        {
          id: "5",
          name: "帮助",
          icon: require("@/assets/personCenter/帮助.png"),
          url: "/Help"
        },
      ],
    };
  },
  computed: {},
  watch: {},
  mounted() {
    setTimeout(() => {
      this.initData()
    },500)
  },
  methods: {
    initData() {
      this.name = this.user.realName
      this.desc = this.user.nickName
    },
    // 退出登录处理
    handleLogout() {
      this.$dialog.confirm({
        title: '退出登录',
        message: '确定要退出登录吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        // 用户点击确定，执行退出登录操作
        this.$toast.loading({
          message: '退出中...',
          forbidClick: true,
          duration: 0
        });

        // 清除token和用户信息
        removeToken()
        this.vuex('user', null)

        // 模拟接口调用延迟
        setTimeout(() => {
          this.$toast.clear();
          this.$toast.success('退出成功');
          // 跳转到登录页
          this.$router.replace('/login');
        }, 1000);
      }).catch(() => {
        // 用户点击取消，不做任何操作
      });
    },
    //菜单点击
    navClick(item) {
      // 如果有特定的action方法，则执行对应方法
      if (item.action && this[item.action]) {
        this[item.action]();
        return;
      }

      // 如果有url则进行跳转
      if (item.url) {
        this.$router.push(item.url);
      }
    },
    // 数据同步
    syncData() {
      this.$toast.loading({
        message: '数据同步中...',
        forbidClick: true,
        duration: 0
      });

      // 模拟同步延迟
      setTimeout(() => {
        this.$toast.clear();
        this.$toast.success('数据同步成功');
      }, 2000);
    },
    // 清除缓存
    clearCache() {
      this.$toast.loading({
        message: '清除缓存中...',
        forbidClick: true,
        duration: 0
      });

      // 模拟清除缓存延迟
      setTimeout(() => {
        // 实际清除缓存逻辑可以在这里添加
        localStorage.clear();
        sessionStorage.clear();

        this.$toast.clear();
        this.$toast.success('缓存清除成功');
      }, 1500);
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #f5f5f5;
  text-align: center;
  padding-bottom: 160px; /* 为退出按钮留出空间 */
  /* 移除固定高度限制，让内容自然展开 */
  .container-head {
    width: 100%;
    height: 220px;
    background-size: cover;
    background: url("~@/assets/personCenter/personCenter_head.png") no-repeat
      center top;
    background-size: 100% auto;
    z-index: 1;
    .head_box {
      text-align: center;
      padding-top: 54px;
      img {
        width: 72px;
        height: 72px;
      }
    }
    .name {
      margin-top: 10px;
      font-size: 16px;
      text-align: center;
      color: #ffffff;
      line-height: 21px;
    }
    .des {
      margin-top: 8px;
      font-size: 14px;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);
      line-height: 21px;
    }
  }
  .list_box {
    background: #fff;
    .list_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      align-content: center;
      padding-left: 22px;
      padding-right: 16px;
      height: 53px;
      border-top: 2px solid #e5e7eb;
      &:first-child {
        border-top: none;
      }
      .item_left {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-content: center;
        img {
          width: 17px;
          height: 17px;
          margin-right: 16px;
        }
      }
    }
  }
  .btn {
    position: fixed;
    bottom: 70px; /* 在tab栏上方留出足够空间 */
    left: 50%;
    transform: translateX(-50%);
    width: 327px;
    height: 48px;
    background: #428ffc;
    border-radius: 8px ;
    font-family: Roboto, Roboto;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    line-height: 48px;
    text-align: center;
    z-index: 999; /* 确保按钮在tab栏下方但可见 */
  }
}
</style>
