{"name": "ygfmobileintegratedentrance", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@babel/runtime": "^7.12.0", "axios": "^0.21.4", "core-js": "^3.8.3", "dayjs": "^1.10.7", "gdt-jsapi": "^1.9.51", "js-cookie": "^3.0.1", "less-loader": "6", "lrz": "^4.9.41", "vant": "2.12.20", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.0", "@babel/plugin-transform-runtime": "^7.26.9", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~4.5.0", "babel-plugin-import": "^1.13.3", "postcss-pxtorem": "^5.0.0", "sass": "1.32.0", "sass-loader": "10.1.0", "sass-resources-loader": "^2.2.1", "svg-sprite-loader": "^6.0.9", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}