<!--黄牛处置-->
<template>
	<div class="yellow-cattle-disposal">
		<van-nav-bar
			title="黄牛处置"
			left-arrow
			fixed
			@click-left="$router.go(-1)"
			class="custom-nav"
		/>
		<van-form class="container">
			<div class="form-section">
				<!-- <van-field
					v-model="form.title"
					label="标题:"
					readonly
					required
					placeholder="请输入标题"
					class="form-field"
				/> -->
				<van-field
					v-model="form.lawbreakers"
					label="违法人姓名:"
					readonly
					required
					placeholder="请选择违法人姓名"
					class="form-field"
				/>
				<!-- <van-field
					v-model="form.identityCard"
					label="身份证:"
					readonly
					required
					placeholder="请选择身份证"
					class="form-field"
				/> -->
				<van-field
					v-model="form.phone"
					label="联系电话:"
					readonly
					required
					placeholder="请输入联系电话"
					class="form-field"
				/>
				<van-field
					v-model="form.happenDate"
					label="登记时间:"
					readonly
					required
					placeholder="请选择登记时间"
					@click="showHappenDate = true"
					class="form-field"
				/>
				<!-- <van-field
					v-model="form.handleDate"
					label="处理时间:"
					readonly
					placeholder="请选择处理时间"
					@click="showHandleDate = true"
					class="form-field"
				/> -->
				<van-field
					v-model="form.completeDate"
					label="办结时间:"
					readonly
					placeholder="请选择办结时间"
					@click="showCompleteDate = true"
					class="form-field"
				/>
				<van-field
					v-model="form.address"
					label="发生地址:"
					readonly
					required
					placeholder="请选择地址"
					class="form-field"
				/>
				<van-field
					v-model="form.lnglat"
					label="经纬度:"
					readonly
					required
					placeholder="请选择地址"
					class="form-field"
				/>
			</div>
			<!-- 间隔 -->
			<div class="gap"></div>
			<div class="form-section">
				<div class="form-item textarea-item">
					<div class="form-item-label">内容描述:<span class="form-item-required">*</span></div>
					<div class="form-item-content">
						<van-field
							v-model="form.content"
							type="textarea"
							rows="5"
							maxlength="300"
							readonly
							placeholder="请输入内容描述..."
							class="content-textarea"
						/>
					</div>
				</div>
				<div class="upload-container">
					<div v-for="(file, index) in happenFile" :key="index" class="upload-item">
						<van-image
							width="100px"
							height="100px"
							fit="cover"
							:src="file.url"
							@click="previewImage(happenFile, index)"
							class="case-image"
						/>
					</div>
				</div>
			</div>
		</van-form>
		<!-- 提示 -->
		<van-toast id="van-toast" />
		<!-- 日历选择器 -->
		<van-calendar
			v-model="showHappenDate"
			@confirm="onHappenDateConfirm"
			:show-confirm="true"
			:round="true"
			:close-on-click-overlay="true"
			:min-date="new Date(2020, 0, 1)"
			:max-date="new Date(2025, 11, 31)"
		/>
		<van-calendar
			v-model="showCompleteDate"
			@confirm="onCompleteDateConfirm"
			:show-confirm="true"
			:round="true"
			:close-on-click-overlay="true"
			:min-date="new Date(2020, 0, 1)"
			:max-date="new Date(2025, 11, 31)"
		/>
	</div>
</template>

<script>
	import { Toast } from 'vant';
	import gps from '@/util/gps.js';
	import { getFileList } from "@/api/common";
	import { getCaseTout,toutAdd } from "@/api/YellowCattleDisposal";

	export default {
		data() {
			return {
				form:{},
				showHappenDate: false,
				showCompleteDate: false,
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				rules: {
					title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
					content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
					lawbreakers: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
					// identityCard: [{ required: true, message: '请输入身份证', trigger: 'blur' }],
					phone: [
						{ required: true, message: '请输入当事人联系电话', trigger: 'blur' },
						// {
						// 	validator: (rule, value, callback) => {
						// 		return this.$u.test.mobile(value);
						// 	},
						// 	message: '手机号码不正确',
						// 	trigger: ['change','blur'],
						// }
					],
					registerTime: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
					address: [{ required: true, message: '请选择地址', trigger: 'change' }],
					lnglat: [{ required: true, message: '请选择地址', trigger: 'change' }],
				},
				happenData: {
					tableName: 'case_tout',
					status: 1
				},
				happenFile: []
			}
		},
		computed: {

		},
		mounted() {
			this.init(this.$route.query);
		},
		methods: {
			init(params) {
				if(params.id){
					Toast.loading({
						message: '加载中...',
						forbidClick: true,
					});

					Promise.all([
						getCaseTout(params.id),
						getFileList({ tableName: 'case_tout', businessId: params.id })
					]).then(res => {
						let formData = res[0].data;
						this.happenFile = res[1].rows.map(item => {
							return { url: `${process.env.VUE_APP_BASE_API}${item.filePath}?id=${item.fileId}` };
						});
						const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`;
						this.form = {...formData, lnglat};
						Toast.clear();
					}).catch(err => {
						console.log(err);
						Toast.clear();
						Toast.fail('加载失败');
					});
				}
			},
			onHappenDateConfirm(date) {
				const year = date.getFullYear();
				const month = date.getMonth() + 1;
				const day = date.getDate();
				const hour = date.getHours();
				const minute = date.getMinutes();
				const second = date.getSeconds();

				this.form.happenDate = `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day} ${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}:${second < 10 ? '0' + second : second}`;
				this.showHappenDate = false;
			},
			onCompleteDateConfirm(date) {
				const year = date.getFullYear();
				const month = date.getMonth() + 1;
				const day = date.getDate();
				const hour = date.getHours();
				const minute = date.getMinutes();
				const second = date.getSeconds();

				this.form.completeDate = `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day} ${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}:${second < 10 ? '0' + second : second}`;
				this.showCompleteDate = false;
			},
			// 预览图片
			previewImage(fileList, index) {
				const images = fileList.map(item => item.url);
				this.$imagePreview({
					images,
					startPosition: index,
					closeable: true
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
.yellow-cattle-disposal {
	padding-top: 46px;
	min-height: 100vh;
	background-color: #f8f8f8;

	.custom-nav {
		background-color: #fff;
		height: 44px;

		:deep(.van-nav-bar__title) {
			font-size: 16px;
			font-weight: 400;
			color: #000;
		}

		:deep(.van-icon) {
			color: #000;
		}
	}

	.container {
		background-color: #f8f8f8;
		padding: 0;
		padding-bottom: 20px;
	}

	.form-section {
		padding: 0;
		background-color: #fff;
		margin-bottom: 8px;
	}

	.form-field {
		padding: 10px 16px;
		margin: 0;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 16px;
			right: 16px;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
			transform: scaleY(0.5);
		}

		:deep(.van-field__label) {
			width: 120px;
			color: #000;
			font-size: 14px;
		}

		:deep(.van-cell__value) {
			flex: 1;
			text-align: right;
			overflow: visible;
			display: flex;
			justify-content: flex-end;
			align-items: center;
		}

		:deep(.van-field__control) {
			color: #576B95;
			text-align: right;
			font-size: 14px;
		}
	}

	.form-item {
		display: flex;
		flex-direction: column;
		padding: 10px 16px;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 16px;
			right: 16px;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
			transform: scaleY(0.5);
		}

		.form-item-label {
			width: 120px;
			color: #000;
			font-size: 14px;
			margin-bottom: 8px;

			.form-item-required {
				color: #ee0a24;
				margin-left: 2px;
			}
		}

		.form-item-content {
			width: 100%;
		}
	}

	.textarea-item {
		.content-textarea {
			padding: 0;
			background-color: #fff;

			:deep(.van-field__control) {
				text-align: left;
				color: #333;
				min-height: 140px;
			}
		}
	}

	.gap {
		height: 10px;
		background-color: #f8f8f8;
	}

	.upload-container {
		display: flex;
		flex-wrap: wrap;
		padding: 10px 0;

		.upload-item {
			margin-right: 10px;
			margin-bottom: 10px;
		}

		.case-image {
			border-radius: 4px;
			overflow: hidden;
		}
	}
}
</style>
