<!--惯用语选择组件-->
<template>
  <div class="idioms-selector">
    <!-- 触发按钮 -->
    <van-button
      :class="{ 'is-plain': plain }"
      :type="type"
      :plain="plain"
      @click="showPopup"
      class="action-btn"
      :disabled="disabled"
    >
      <slot>{{ buttonText }}</slot>
    </van-button>

    <!-- 惯用语选择弹窗 -->
    <van-popup
      v-model="visible"
      round
      position="bottom"
      :style="{ maxHeight: '70%' }"
      class="idioms-popup"
    >
      <div class="popup-header">
        <div class="popup-title">选择惯用语</div>
        <van-icon name="cross" class="close-icon" @click="closePopup" />
      </div>

      <div class="popup-content">
        <div v-if="loading" class="loading-container">
          <van-loading type="spinner" color="#1989fa" />
          <span class="loading-text">加载中...</span>
        </div>

        <div v-else-if="idiomsList.length === 0" class="empty-container">
          <van-empty description="暂无惯用语" />
          <div class="empty-action">
            <van-button plain type="info" size="small" @click="goToSettings">
              去添加
            </van-button>
          </div>
        </div>

        <div v-else class="idioms-list">
          <div
            v-for="(item, index) in idiomsList"
            :key="index"
            class="idiom-item"
            @click="selectIdiom(item.phrase)"
          >
            <div class="idiom-content">{{ item.phrase }}</div>
          </div>
        </div>
      </div>

      <div class="popup-footer">
        <van-button block type="info" @click="closePopup">取消</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getPhraseList } from '@/api/common'

export default {
  name: 'IdiomsSelector',
  props: {
    buttonText: {
      type: String,
      default: '选择惯用语'
    },
    type: {
      type: String,
      default: 'info'
    },
    plain: {
      type: Boolean,
      default: true
    },
    target: {
      // 输入框的值，用于绑定选中后的惯用语
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      idiomsList: [],
      loading: false,
      // 分页参数
      queryParams: {
        pageNum: 1,
        pageSize: 50
      }
    }
  },
  methods: {
    // 显示弹窗
    showPopup() {
      if (this.disabled) return;
      this.visible = true;
      this.getIdiomsList();
    },

    // 关闭弹窗
    closePopup() {
      this.visible = false;
    },

    // 获取惯用语列表
    async getIdiomsList() {
      try {
        this.loading = true;

        // 调用实际API
        const res = await getPhraseList(this.queryParams);

        if (res.code === 200) {
          this.idiomsList = res.rows || [];
        } else {
          this.$toast.fail(res.msg || '获取惯用语失败');
        }
      } catch (error) {
        console.error('获取惯用语列表失败:', error);
        this.$toast.fail('获取惯用语失败');
      } finally {
        this.loading = false;
      }
    },

    // 选择惯用语
    selectIdiom(content) {
      this.$emit('select', content);
      this.closePopup();
    },

    // 跳转到惯用语设置页面
    goToSettings() {
      this.$router.push('/IdiomaticExpressions');
      this.closePopup();
    }
  }
}
</script>

<style lang="scss" scoped>
.idioms-selector {
  display: inline-block;

  // 按钮样式
  .action-btn {
    height: 32px;
    padding: 0 15px;
    font-size: 13px;
    border-radius: 4px;
    background-color: #e8f3ff;
    border-color: #e8f3ff;
    color: #1989fa;
  }

  .selector-btn {
    height: 30px;
    border-radius: 4px;
    font-size: 14px;

    &.is-plain {
      color: #1989fa;
      border-color: #1989fa;
    }
  }
}

.idioms-popup {
  border-radius: 16px 16px 0 0;
  overflow: hidden;

  .popup-header {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    height: 50px;
    border-bottom: 1px solid #eee;

    .popup-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .close-icon {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 18px;
      color: #999;
    }
  }

  .popup-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 0 16px;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;

      .loading-text {
        margin-top: 12px;
        color: #999;
        font-size: 14px;
      }
    }

    .empty-container {
      padding: 30px 0;

      .empty-action {
        display: flex;
        justify-content: center;
        margin-top: 16px;
      }
    }

    .idioms-list {
      padding: 10px 0;

      .idiom-item {
        text-align: left;
        padding: 12px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .idiom-content {
          font-size: 14px;
          color: #333;
          line-height: 1.5;
        }

        &:active {
          background-color: #f5f7fa;
        }
      }
    }
  }

  .popup-footer {
    padding: 10px 16px;
    border-top: 1px solid #f5f5f5;
  }
}
</style>
