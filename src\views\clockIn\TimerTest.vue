<template>
  <div class="timer-test">
    <van-nav-bar title="定时器测试" left-arrow @click-left="$router.go(-1)" />

    <div class="test-content">
      <van-cell-group title="定时器状态">
        <van-cell title="定时器状态" :value="timerStatus.isRunning ? '运行中' : '已停止'" />
        <van-cell title="用户ID" :value="timerStatus.userId || '未设置'" />
        <van-cell title="用户类型" :value="timerStatus.userType || '未设置'" />
        <van-cell title="回调数量" :value="timerStatus.callbackCount" />
        <van-cell title="启动时间" :value="timerStatus.startTime || '未启动'" />
      </van-cell-group>

      <van-cell-group title="全局管理器状态">
        <van-cell title="管理器状态" :value="globalStatus.isInitialized ? '已初始化' : '未初始化'" />
        <van-cell title="当前用户" :value="globalStatus.currentUser?.userName || '未设置'" />
        <van-cell title="用户ID" :value="globalStatus.currentUser?.userId || '未设置'" />
        <van-cell title="用户类型" :value="globalStatus.currentUser?.userType || '未设置'" />
      </van-cell-group>

      <div class="button-group">
        <van-button
          type="primary"
          block
          @click="startTimer"
          :disabled="timerStatus.isRunning"
        >
          启动定时器
        </van-button>

        <van-button
          type="danger"
          block
          @click="stopTimer"
          :disabled="!timerStatus.isRunning"
        >
          停止定时器
        </van-button>

        <van-button
          type="default"
          block
          @click="refreshStatus"
        >
          刷新状态
        </van-button>

        <van-button
          type="info"
          block
          @click="testLocationReport"
        >
          测试位置上报
        </van-button>

        <van-button
          type="warning"
          block
          @click="testGlobalManager"
        >
          测试全局管理器
        </van-button>
      </div>

      <div class="log-section">
        <h3>定时器日志</h3>
        <div class="log-container">
          <div
            v-for="(log, index) in logs"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>

        <van-button
          type="warning"
          size="small"
          @click="clearLogs"
        >
          清除日志
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import locationTimer from '@/util/locationTimer'
import { personPosition } from '@/api/staffLocation'

export default {
  name: 'TimerTest',
  data() {
    return {
      timerStatus: {
        isRunning: false,
        userId: null,
        userType: null,
        startTime: null,
        callbackCount: 0
      },
      globalStatus: {
        isInitialized: false,
        currentUser: null,
        timerStatus: {}
      },
      logs: []
    }
  },
  mounted() {
    this.refreshStatus()

    // 添加测试回调
    locationTimer.addCallback(this.testCallback)
  },
  beforeDestroy() {
    // 移除测试回调
    locationTimer.removeCallback(this.testCallback)
  },
  methods: {
    testCallback(data) {
      this.addLog(`定时器触发: ${JSON.stringify(data)}`)
    },

    startTimer() {
      locationTimer.start({
        userId: 'test-user-123',
        userType: '1',
        callback: this.testCallback
      })

      this.addLog('定时器已启动')
      this.refreshStatus()
    },

    stopTimer() {
      locationTimer.stop()
      this.addLog('定时器已停止')
      this.refreshStatus()
    },

    refreshStatus() {
      this.timerStatus = locationTimer.getStatus()
      if (this.$globalLocationManager) {
        this.globalStatus = this.$globalLocationManager.getStatus()
      }
    },

    addLog(message) {
      const now = new Date()
      this.logs.unshift({
        time: now.toLocaleTimeString(),
        message
      })

      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },

    clearLogs() {
      this.logs = []
    },

    async testLocationReport() {
      try {
        // 获取当前位置
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(async (position) => {
            const { latitude, longitude } = position.coords

            const params = {
              lat: latitude.toString(),
              lon: longitude.toString(),
              address: '测试地址 - 定时器测试页面'
            }

            this.addLog(`开始测试位置上报: ${JSON.stringify(params)}`)

            try {
              await personPosition(params)
              this.addLog('位置上报测试成功')
            } catch (error) {
              this.addLog(`位置上报测试失败: ${error.message}`)
            }
          }, (error) => {
            this.addLog(`获取位置失败: ${error.message}`)
          })
        } else {
          this.addLog('浏览器不支持地理位置获取')
        }
      } catch (error) {
        this.addLog(`测试位置上报异常: ${error.message}`)
      }
    },

    testGlobalManager() {
      if (this.$globalLocationManager) {
        this.addLog('触发全局管理器状态检查')
        this.$globalLocationManager.forceCheckClockStatus()

        setTimeout(() => {
          this.refreshStatus()
          this.addLog('全局管理器状态已刷新')
        }, 1000)
      } else {
        this.addLog('全局管理器未初始化')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.timer-test {
  min-height: 100vh;
  background: #f5f5f5;

  .test-content {
    padding: 16px;

    .button-group {
      margin: 20px 0;

      .van-button {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .log-section {
      background: #fff;
      border-radius: 8px;
      padding: 16px;

      h3 {
        margin: 0 0 12px 0;
        font-size: 16px;
        color: #333;
      }

      .log-container {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 12px;
        background: #f9f9f9;

        .log-item {
          display: block;
          margin-bottom: 4px;
          font-size: 12px;
          line-height: 1.4;

          .log-time {
            color: #666;
            margin-right: 8px;
          }

          .log-message {
            color: #333;
          }
        }
      }
    }
  }
}
</style>
