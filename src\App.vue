<template>
  <div id="app">
    <router-view />
    <!-- 全局定位状态组件 -->
    <GlobalLocationStatus />
  </div>
</template>

<script>
import setRem from "@/util/rem";
import GlobalLocationStatus from "@/components/GlobalLocationStatus.vue";

export default {
  name: "App",
  components: {
    GlobalLocationStatus
  },
  data() {
    return {
      params: "",
      routerArr: [],
    };
  },
  watch: {
    "$route.path": {
      handler(toPath, fromPath) {
        console.log("当前页面路由地址：" + toPath);
        console.log("上一个路由地址：" + fromPath);
        this.watchRouter();
      },
    },
  },
  onLaunch() {},
  mounted() {
    this.addListener();
    setRem();
    // 改变窗口大小时重新设置 rem
    window.onresize = () => {
      setRem();
    };
  },
  methods: {
    watchRouter() {
      setTimeout(() => {
        console.log("路由跳转");
        // const pages = getCurrentPages();
        // const currentPage = pages[pages.length - 1];
        let canExitType = "";
        // if (!currentPage) return;
        if (this.$route.path == "/" || this.$route.path == "/login") {
          canExitType = "canExit";
          console.log("canExit");
        } else {
          canExitType = "canNotExit";
          console.log("canNotExit");
        }

        window.parent.postMessage(
          {
            cmd: "route_change",
            currentPage: this.$route.path,
            canExitType: canExitType,
          },
          "*"
        );
      }, 1000); // 延迟去获得跳转后的页面路由
    },
    addListener() {
      window.addEventListener("message", (event) => {
        // const pages = getCurrentPages();
        // const currentPage = pages[pages.length - 1];
        if (event.data.cmd == "navi_back") {
          console.log("ccccccccccccc", this.$route.path);
          if (this.$route.path == "/" || this.$route.path == "/login") {
          } else {
            history.back();
          }

          // }
        }
      });
    },
  },
};
</script>

<style>
#app {
  min-height: 100vh;
  margin: 0;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN-Medium,
    Source Han Sans CN-Bold, Source Han Sans CN, sans-serif;
  color: #333;
  background: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
