import request from '@/util/request'

//获取用户列表
//status(string)
// 说明：
// 在线/离线状态 0离线 1在线 ''全部
export function groupByUserList(params) {
  return request({
    url: `/zhcg/personPosition/groupByUser`,
    method: 'get',
    params
  })
}

//定位回传
//传参示例
// {
//   "lat": "29.0776826",
//   "address": "辽宁省永州市文水县",
//   "lon": "119.640189"
// }
export function personPosition(data) {
  return request({
    url: `/zhcg/personPosition`,
    method: 'post',
    data
  })
}

//获取人员详情
export function getPersonDetail(id) {
  return request({
    url: `/zhcg/personPosition/${id}`,
    method: 'get'
  })
}
