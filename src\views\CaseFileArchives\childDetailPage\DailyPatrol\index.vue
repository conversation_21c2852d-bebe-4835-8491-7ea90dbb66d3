<!--日常巡查-运管执法-->
<template>
	<div class="daily-patrol">

    <!-- 导航栏 -->
    <van-nav-bar
        title="日常巡查"
        left-arrow
        fixed
        @click-left="$router.go(-1)"
    />

		<van-form class="container" label-width="200px">
			<div class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title">
					<u-input v-model="form.title" placeholder="请输入标题" disabled />
				</u-form-item> -->
				<van-field
					v-model="form.userName"
					label="巡查人员:"
					readonly
					placeholder="巡查人员"
				/>
				<van-field
					v-model="form.userNames"
					label="辅助人员:"
					placeholder="辅助人员"
				/>
				<van-field
					v-model="form.inspectionTime"
					label="巡查时间:"
					readonly
					placeholder="请选择巡查时间"
				/>
				<van-field
					v-model="form.party"
					label="当事人:"
					readonly
					placeholder="请输入当事人姓名"
				/>
				<van-field
					v-model="form.carNo"
					label="车辆车牌:"
					readonly
					placeholder="请输入车辆车牌"
				/>
				<van-field
					v-model="form.models"
					label="车辆品牌:"
					readonly
					placeholder="请输入车辆品牌"
				/>
				<van-field
					v-model="form.carTypeName"
					label="车辆类型:"
					readonly
					placeholder="请选择车辆类型"
				/>
				<van-field
					v-model="form.operationCompany"
					label="营运公司:"
					placeholder="请输入营运公司"
				/>
				<van-field
					v-model="form.address"
					label="发生地址:"
					readonly
					placeholder="请选择地址"
				/>
			</div>
			<!-- 间隔 -->
			<div class="gap"></div>
			<div class="p-lr-30">
				<div class="form-item radio-item">
					<div class="form-item-label">是否有问题:</div>
					<div class="form-item-content">
						<van-radio-group v-model="form.isProblem" direction="horizontal" disabled>
							<van-radio name="1">有</van-radio>
							<van-radio name="0">无</van-radio>
						</van-radio-group>
					</div>
				</div>

				<van-field
					v-if="form.isProblem == 1"
					v-model="form.problemTypeName"
					label="问题类型:"
					placeholder="请选择问题类型"
					required
				/>

				<div class="form-item textarea-item">
					<div class="form-item-label">检查概况:</div>
					<div class="form-item-content">
						<van-field
							v-model="form.content"
							type="textarea"
							maxlength="300"
							rows="5"
							placeholder="请输入检查概况..."
							readonly
							class="content-textarea"
						/>
					</div>
				</div>

				<div class="upload-container">
					<div v-for="(file, index) in happenFile" :key="index" class="upload-item">
						<van-image
							width="100px"
							height="100px"
							fit="cover"
							:src="file.url"
							@click="previewImage(index)"
						/>
					</div>
				</div>
			</div>
		</van-form>

		<!-- 提示 -->
		<van-toast id="van-toast" />
	</div>
</template>

<script>
import { getTransport } from "@/api/DailyPatrol";
import { getFileList } from "@/api/common";
import { Toast } from 'vant';

export default {
	data() {
		return {
			params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
			showCarType: false,
			transportCarType: [],
			showInspectionType: false,
			inspectionTypeList: [],
			showInspectionTime: false,
			isShowFTaxiType: false,
			isShowSTaxiType: false,
			form: {},
			happenData: {
				tableName: 'case_transport',
				status: 1
			},
			happenFile: [],
			showList: false,
			problemTypeList: []
		}
	},
	computed: {

	},
  mounted() {
    this.init(this.$route.query)
  },
  methods: {
    async init(params) {
      // 获取车辆类型
      Toast.loading({
        message: '获取系统配置数据',
        forbidClick: true,
      });

      await this.getDicts('transport_car_type').then(res => {
        this.transportCarType = res.data.map(item => {
          item.text = item.dictLabel
          return item
        })
        Toast.clear();
      }).catch(() => {
        Toast.clear();
      })

      // 获取业务数据
      if (params.id) {
        Toast.loading({
          message: '加载中...',
          forbidClick: true,
        });

        Promise.all([
          getTransport(params.id),
          getFileList({ tableName: 'case_transport', status: 1, businessId: params.id })
        ]).then(resAry => {
          const formData = resAry[0].data
          const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
          this.form = { ...formData, lnglat }
          this.happenFile = resAry[1].rows.map(item => {
            return { url: `${process.env.VUE_APP_BASE_API}${item.filePath}?id=${item.fileId}` }
          })
          Toast.clear();
        }).catch(() => {
          Toast.clear();
        })
      } else {
        const timestamp = new Date().getTime()
        const inspectionTime = this.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
        this.form = { inspectionTime, userName: this.user.userName, userId: this.user.userId }
      }
    },

    // 预览图片
    previewImage(index) {
      const images = this.happenFile.map(item => item.url);
      this.$imagePreview({
        images,
        startPosition: index,
        closeable: true
      });
    }
  },
}
</script>

<style lang="scss" scoped>
.daily-patrol {
	.container {
		padding-bottom: 145px;
	}

	.p-lr-30 {
		padding: 0 5px;
		background-color: #fff;
	}

	.gap {
		height: 20px;
		background-color: #F5F5F5;
	}

	.btn-box {
		width: 100%;
		padding: 7px 15px;
		position: fixed;
		bottom: 0;
		background-color: #FFFFFF;
		z-index: 10;
	}

	.form-item {
		display: flex;
		padding: 10px 16px;
		line-height: 24px;

		.form-item-label {
			width: 200px;
			color: #808080;
			font-size: 15px;
			flex: none;
		}

		.form-item-content {
			flex: 1;
		}
	}

	.radio-item {
		.van-radio {
			margin-right: 20px;
		}
	}

	.textarea-item {
		align-items: flex-start;

		.form-item-label {
			padding-top: 10px;
		}

		.content-textarea {
			height: 140px;
		}
	}

	.upload-container {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10px;
    margin-left: 12px;
		.upload-item {
			margin-right: 10px;
			margin-bottom: 10px;
		}
	}
}
</style>
