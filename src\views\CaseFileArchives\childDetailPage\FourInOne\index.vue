<!--四位一体-->
<template>
	<div class="four-in-one">

    <!-- 导航栏 -->
    <van-nav-bar
        title="四位一体"
        left-arrow
        fixed
        @click-left="$router.go(-1)"
    />

		<van-form class="container" label-width="200px">
			<div class="p-lr-30">
				<!-- <u-form-item label="标题:" prop="title">
					<u-input v-model="form.title" placeholder="请输入标题" disabled/>
				</u-form-item> -->
				<van-field
					v-model="form.typeText"
					label="类型:"
					placeholder="请选择类型"
				/>
				<van-field
					v-model="form.userName"
					label="发起人员:"
					readonly
					placeholder="请选择发起人员"
				/>
				<van-field
					v-model="form.phone"
					label="联系电话:"
					readonly
					placeholder="请输入发起人员联系电话"
				/>
				<van-field
					v-model="form.happenDate"
					label="发生时间:"
					placeholder="请选择发生时间"
				/>
				<van-field
					v-model="form.address"
					label="发生地址:"
					placeholder="请选择地址"
				/>
				<van-field
					v-model="form.lnglat"
					label="经纬度:"
					readonly
					placeholder="请选择地址"
				/>
			</div>
			<!-- 间隔 -->
			<div class="gap"></div>
			<div class="p-lr-30">
				<div class="form-item textarea-item">
					<div class="form-item-label">内容描述:</div>
					<div class="form-item-content">
						<van-field
							v-model="form.content"
							type="textarea"
							rows="5"
							maxlength="300"
							readonly
							placeholder="请输入内容描述..."
							class="content-textarea"
						/>
					</div>
				</div>

				<div class="upload-container">
					<div v-for="(file, index) in happenFile" :key="index" class="upload-item">
						<van-image
							width="100px"
							height="100px"
							fit="cover"
							:src="file.url"
							@click="previewImage(happenFile, index)"
						/>
					</div>
				</div>
			</div>
			<!-- 间隔 -->
			<div class="gap"></div>
			<div class="p-lr-30">
				<van-field
					v-model="form.handleUserName"
					label="处理人员:"
					placeholder="请选择处理人员"
				/>
				<van-field
					v-model="form.handleDate"
					label="处理完成时间:"
					placeholder="请输入处理结果"
				/>

				<div class="form-item textarea-item">
					<div class="form-item-label">处理结果:</div>
					<div class="form-item-content">
						<van-field
							v-model="form.handleContent"
							type="textarea"
							rows="5"
							maxlength="300"
							readonly
							placeholder="请输入处理结果..."
							class="content-textarea"
						/>
					</div>
				</div>

				<div class="upload-container">
					<div v-for="(file, index) in handleFile" :key="index" class="upload-item">
						<van-image
							width="100px"
							height="100px"
							fit="cover"
							:src="file.url"
							@click="previewImage(handleFile, index)"
						/>
					</div>
				</div>
			</div>
			<div class="gap"></div>
			<div class="p-lr-30" v-if="form.rejectContent">
				<div class="form-item textarea-item">
					<div class="form-item-label">驳回原因:</div>
					<div class="form-item-content">
						<van-field
							v-model="form.rejectContent"
							type="textarea"
							rows="5"
							maxlength="300"
							readonly
							class="content-textarea"
						/>
					</div>
				</div>
			</div>
			<div class="gap"></div>
		</van-form>

		<!-- 撤回选项 -->
		<van-popup v-model="showPopup" position="bottom" closeable>
			<div class="popup-container">
				<div class="popup-title">撤回原因</div>
				<van-field
					v-model="form.rejectContent"
					type="textarea"
					rows="5"
					maxlength="300"
					class="reject-textarea"
				/>
				<div class="popup-footer">
					<van-button type="primary" round block @click="handleRevoke">确定</van-button>
				</div>
			</div>
		</van-popup>

		<!-- 提交按钮 -->
		<div class="btn-box" v-if="showBtn">
			<van-button type="danger" block class="revoke-btn" round @click="showPopup = true">驳回</van-button>
			<van-button type="primary" block class="submit-btn" round @click="handleSubmit">办结</van-button>
		</div>

		<!-- 提示 -->
		<van-toast id="van-toast" />
	</div>
</template>

<script>
import { Toast } from 'vant';
import { getFileList } from "@/api/common";
import {caseEdit, getCaseDetail, caseRevoke} from "@/api/FourInOne";

export default {
	data() {
		return {
			params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
			typeList: [
				{ type: 3, text: '市政道路' },
				{ type: 4, text: '园林' },
				{ type: 1, text: '绿化' },
				{ type: 2, text: '环卫保洁' }
			],
			form: {},
			happenFile: [],
			handleFile: [],
			showPopup: false,
			showBtn: false
		}
	},
	mounted() {
		this.init(this.$route.query);
	},
	methods: {
		async init(params) {
			if (params.id) {
				Toast.loading({
					message: '加载中...',
					forbidClick: true,
				});

				Promise.all([
					getCaseDetail(params.id),
					getFileList({ tableName: 'case_four_in_one', businessId: params.id })
				]).then(resAry => {
					const formData = resAry[0].data
					const typeText = this.typeList.find(item => item.type == formData.type)
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					const timestamp = new Date().getTime()
					const handleDate = this.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
					if (!formData.handleDate) formData.handleDate = handleDate
					if (formData.status == 2 && formData.userId == this.user.userId) this.showBtn = true
					this.form = { ...formData, typeText: typeText ? typeText.text : '', lnglat }
					// 图片部分
					resAry[1].rows.forEach(item => {
						const url = { url: `${process.env.VUE_APP_BASE_API}${item.filePath}?id=${item.fileId}` }
						if (item.status == 1) {
							this.happenFile.push(url)
						} else {
							this.handleFile.push(url)
						}
					})
					Toast.clear();
				}).catch(() => {
					Toast.clear();
				})
			} else {
				const timestamp = new Date().getTime()
				const happenDate = this.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = { happenDate, userName: this.user.userName, userId: this.user.userId }
			}
		},

		handleSubmit() {
			this.$dialog.confirm({
				title: '提示',
				message: '是否确认完成？',
			}).then(() => {
				const params = { ...this.form, status: 9 }
				Toast.loading({
					message: '数据上传中',
					forbidClick: true,
				});

				caseEdit(params).then(res => {
					Toast.success('操作成功');
          setTimeout(() => {
            this.$router.go(-1);
          }, 1000);
				}).catch(() => {
					Toast.clear();
				})
			}).catch(() => {
				// 取消操作
			});
		},

		handleRevoke() {
			const { fourId, rejectContent } = this.form
			if (!rejectContent || rejectContent.trim() === '') {
				Toast.fail('请输入驳回原因');
				return
			}

			this.$dialog.confirm({
				title: '提示',
				message: '是否确认驳回？',
			}).then(() => {
				const params = { fourId, rejectContent, status: 0 }
				Toast.loading({
					message: '数据上传中',
					forbidClick: true,
				});

				caseRevoke(params).then(res => {
					Toast.success('操作成功');
          setTimeout(() => {
            this.$router.go(-1);
          }, 1000);
				}).catch(() => {
					Toast.clear();
				})
			}).catch(() => {
				// 取消操作
			});
		},

		// 预览图片
		previewImage(fileList, index) {
			const images = fileList.map(item => item.url);
			this.$imagePreview({
				images,
				startPosition: index,
				closeable: true
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.four-in-one {
	.container {
		padding-bottom: 115px;
	}

	.p-lr-30 {
		padding: 0 5px;
		background-color: #fff;
	}

	.gap {
		height: 20px;
		background-color: #F5F5F5;
	}

	.btn-box {
		width: 100%;
		padding: 7px 15px;
		position: fixed;
		bottom: 0;
		background-color: #FFFFFF;
		z-index: 10;
		display: flex;
		justify-content: space-between;

		.revoke-btn {
			margin-right: 15px;
			flex: 1;
			height: 43px;
			background-color: #fa3534;
			border-color: #fa3534;
		}

		.submit-btn {
			flex: 1;
			height: 43px;
			background-color: #327BF0;
			border-color: #327BF0;
		}
	}

	.form-item {
		display: flex;
		padding: 10px 16px;
		line-height: 24px;

		.form-item-label {
			width: 200px;
			color: #808080;
			font-size: 15px;
			flex: none;
		}

		.form-item-content {
			flex: 1;
		}
	}

	.textarea-item {
		align-items: flex-start;

		.form-item-label {
			padding-top: 10px;
		}

		.content-textarea {
			height: 140px;
		}
	}

	.upload-container {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10px;
		margin-left: 12px;

		.upload-item {
			margin-right: 10px;
			margin-bottom: 10px;
		}
	}

	.popup-container {
		padding: 20px;

		.popup-title {
			font-size: 16px;
			font-weight: 500;
			margin-bottom: 15px;
		}

		.reject-textarea {
			height: 140px;
			margin-bottom: 20px;
		}

		.popup-footer {
			margin-top: 24px;
		}
	}
}
</style>
