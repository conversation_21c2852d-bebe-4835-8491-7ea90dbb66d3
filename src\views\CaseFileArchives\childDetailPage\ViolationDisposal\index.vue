<!--违规处置-->
<template>
	<div class="violation-disposal">
		<van-nav-bar
			title="违规处置"
			left-arrow
			fixed
			@click-left="$router.go(-1)"
			class="custom-nav"
		/>
		<van-form class="container">
			<div class="form-section">
				<van-field
					v-model="form.party"
					label="当事人:"
					readonly
					required
					placeholder="请输入当事人姓名"
					class="form-field"
				/>
				<!-- <van-field
					v-model="form.identityCard"
					label="身份证:"
					readonly
					placeholder="请输入当事人身份证号"
					class="form-field"
				/> -->
				<van-field
					v-model="form.phone"
					label="联系电话:"
					readonly
					placeholder="请输入当事人联系电话"
					class="form-field"
				/>
				<van-field
					v-model="form.carNo"
					label="车辆车牌:"
					readonly
					required
					placeholder="请输入车辆车牌"
					class="form-field"
				/>
				<van-field
					v-model="form.models"
					label="车辆品牌:"
					readonly
					placeholder="请输入车辆品牌"
					class="form-field"
				/>
				<van-field
					v-model="form.operationCompany"
					label="营运公司:"
					readonly
					placeholder="请输入营运公司"
					class="form-field"
				/>
				<van-field
					v-model="form.certificateNo"
					label="从业资格证号:"
					readonly
					placeholder="请输入从业资格证号"
					class="form-field"
				/>
				<van-field
					v-model="form.transportCertificate"
					label="道路运输证号:"
					readonly
					placeholder="请输入道路运输证号"
					class="form-field"
				/>
			</div>
			<!-- 间隔 -->
			<div class="gap"></div>
			<div class="form-section">
				<!-- <van-field
					v-model="form.title"
					label="标题:"
					required
					placeholder="请输入标题"
					class="form-field"
				/> -->
				<van-field
					v-model="form.userName"
					label="上报人员:"
					readonly
					required
					placeholder="上报人员"
					class="form-field"
				/>
				<van-field
					v-model="form.userNames"
					label="辅助人员:"
					placeholder="辅助人员"
					class="form-field"
				/>
				<van-field
					v-model="form.inspectionTime"
					label="上报时间:"
					readonly
					required
					placeholder="请选择上报时间"
					class="form-field"
				/>
				<van-field
					v-model="form.carTypeName"
					label="车辆类型:"
					readonly
					required
					placeholder="请选择车辆类型"
					class="form-field"
				/>

				<div class="form-item checkbox-item">
					<div class="form-item-label">违规类型:<span class="form-item-required">*</span></div>
					<div class="form-item-content">
						<span v-if="!inspectionTypeList.length" class="empty-text">请选择违规类型</span>
						<div class="checkbox-group">
							<van-checkbox
								v-model="item.checked"
								disabled
								v-for="(item, index) in inspectionTypeList"
								:key="index"
								:name="item.dictValue"
								class="checkbox-item"
							>{{item.dictLabel}}</van-checkbox>
						</div>
					</div>
				</div>

				<van-field
					v-model="form.address"
					label="发生地址:"
					readonly
					required
					placeholder="请选择地址"
					class="form-field"
				/>
				<van-field
					v-model="form.lnglat"
					label="经纬度:"
					readonly
					required
					placeholder="请选择地址"
					class="form-field"
				/>
			</div>
			<!-- 间隔 -->
			<div class="gap"></div>
			<div class="form-section">
				<div class="form-item textarea-item">
					<div class="form-item-label">违规内容:<span class="form-item-required">*</span></div>
					<div class="form-item-content">
						<van-field
							v-model="form.content"
							type="textarea"
							rows="5"
							maxlength="300"
							readonly
							placeholder="请输入违规内容..."
							class="content-textarea"
						/>
					</div>
				</div>
				<div class="upload-container">
					<div v-for="(file, index) in happenFile" :key="index" class="upload-item">
						<van-image
							width="100px"
							height="100px"
							fit="cover"
							:src="file.url"
							@click="previewImage(happenFile, index)"
							class="case-image"
						/>
					</div>
				</div>
			</div>
			<!-- 违规文件部分 -->
			<div v-for="(item, idx) in exFile" :key="idx">
				<div class="gap"></div>
				<div class="form-section">
					<div class="form-item file-item">
						<div class="form-item-label">{{item.label}}:</div>
						<div class="form-item-content">
							<div v-if="!item.fileList.length" class="empty-file">无</div>
							<div v-else class="upload-container">
								<div v-for="(file, fileIdx) in item.fileList" :key="fileIdx" class="upload-item">
									<van-image
										width="100px"
										height="100px"
										fit="cover"
										:src="file.url"
										@click="previewImage(item.fileList, fileIdx)"
										class="case-image"
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</van-form>
		<!-- 提示 -->
		<van-toast id="van-toast" />
	</div>
</template>

<script>
import { Toast } from 'vant';
import { getTransport } from "@/api/ViolationDisposal";
import { getFileList } from "@/api/common";

export default {
	data() {
		return {
			params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
			showCarType: false,
			transportCarType: [],
			showInspectionType: false,
			inspectionTypeList: [],
			showInspectionTime: false,
			isShowFTaxiType: false,
			isShowSTaxiType: false,
			form: {},
			happenData: {
				tableName: 'case_transport',
				status: 1
			},
			happenFile: [],
			exFile: []
		}
	},
	computed: {

	},
	mounted() {
		this.init(this.$route.query);
	},
	methods: {
		setFileType(type) {
			if (type == 4) {
				/* 网约车，修改上传文件类型 */
				this.exFile = [
					{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
					{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
					{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
					{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
					{ key: 8, label: '查封通知书', data: { tableName: 'case_transport', status: 8 }, fileList: [] },
					// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
				]
			} else if (type == 5) {
				/* 黑车，修改上传文件类型 */
				this.exFile = [
					{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
					{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
					{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
					{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
					{ key: 9, label: '行政强制措施现场笔录', data: { tableName: 'case_transport', status: 9 }, fileList: [] },
					{ key: 10, label: '扣押决定书', data: { tableName: 'case_transport', status: 10 }, fileList: [] },
					// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
				]
			} else {
				/* 其余为默认 */
				this.exFile = [
					{ key: 2, label: '现场笔录', data: { tableName: 'case_transport', status: 2 }, fileList: [] },
					{ key: 3, label: '乘客询问笔录', data: { tableName: 'case_transport', status: 3 }, fileList: [] },
					{ key: 4, label: '驾驶员询问笔录', data: { tableName: 'case_transport', status: 4 }, fileList: [] },
					{ key: 5, label: '调查通知书', data: { tableName: 'case_transport', status: 5 }, fileList: [] },
					// { key: 6, label: '先行登记保存证据通知书', data: { tableName: 'case_transport', status: 6 }, fileList: [] },
					// { key: 7, label: '图片证据', data: { tableName: 'case_transport', status: 7 }, fileList: [] },
				]
			}
		},
		async init(params) {
			// 获取车辆类型
			Toast.loading({
				message: '获取系统配置数据',
				forbidClick: true,
			});

			try {
				const res = await this.getDicts('transport_car_type');
				this.transportCarType = res.data.map(item => {
					item.text = item.dictLabel;
					return item;
				});
				Toast.clear();

				// 获取业务数据
				if (params.id) {
					Toast.loading({
						message: '加载中...',
						forbidClick: true,
					});

					try {
						const [resData, fileRes, dictRes] = await Promise.all([
							getTransport(params.id),
							getFileList({ tableName: 'case_transport', businessId: params.id }),
							this.getDicts('transport_car_type')
						]);

						const formData = resData.data;
						const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`;
						this.form = { ...formData, lnglat };

						// 设置文件分类
						this.setFileType(this.form.carType);

						fileRes.rows.map(item => {
							const obj = { url: `${process.env.VUE_APP_BASE_API}${item.filePath}?id=${item.fileId}` };
							if (item.status == 1) {
								this.happenFile.push(obj);
							} else {
								this.exFile.forEach(file => {
									if (file.data.status == item.status) {
										file.fileList.push(obj);
									}
								});
							}
						});

						// 设置违规类型
						let remark = '';
						this.transportCarType = dictRes.data.map(item => {
							item.text = item.dictLabel;
							if (item.dictValue == resData.data.carType) {
								remark = item.remark;
							}
							return item;
						});

						// 获取巡查类型
						if (remark) {
							const dictResponse = await this.getDicts(remark);
							this.inspectionTypeList = dictResponse.data.map(item => {
								item.checked = this.form.inspectionType.includes(item.dictValue);
								return item;
							});
						}

						Toast.clear();
					} catch (error) {
						console.error(error);
						Toast.clear();
						Toast.fail('加载失败');
					}
				} else {
					const timestamp = new Date().getTime();
					const inspectionTime = this.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss');
					this.form = { inspectionTime, userName: this.user.userName, userId: this.user.userId };
				}
			} catch (error) {
				console.error(error);
				Toast.clear();
				Toast.fail('获取系统配置失败');
			}
		},
		// 预览图片
		previewImage(fileList, index) {
			const images = fileList.map(item => item.url);
			this.$imagePreview({
				images,
				startPosition: index,
				closeable: true
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.violation-disposal {
	padding-top: 46px;
	min-height: 100vh;
	background-color: #f8f8f8;

	.custom-nav {
		background-color: #fff;
		height: 44px;

		:deep(.van-nav-bar__title) {
			font-size: 16px;
			font-weight: 400;
			color: #000;
		}

		:deep(.van-icon) {
			color: #000;
		}
	}

	.container {
		background-color: #f8f8f8;
		padding: 0;
	}

	.form-section {
		padding: 0;
		background-color: #fff;
		margin-bottom: 8px;
	}

	.form-field {
		padding: 10px 16px;
		margin: 0;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 16px;
			right: 16px;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
			transform: scaleY(0.5);
		}

		:deep(.van-field__label) {
			width: 120px;
			color: #000;
			font-size: 14px;
		}

		:deep(.van-cell__value) {
			flex: 1;
			text-align: right;
			overflow: visible;
			display: flex;
			justify-content: flex-end;
			align-items: center;
		}

		:deep(.van-field__control) {
			color: #576B95;
			text-align: right;
			font-size: 14px;
		}
	}

	.form-item {
		display: flex;
		flex-direction: column;
		padding: 10px 16px;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 16px;
			right: 16px;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
			transform: scaleY(0.5);
		}

		.form-item-label {
			width: 120px;
			color: #000;
			font-size: 14px;
			margin-bottom: 8px;

			.form-item-required {
				color: #ee0a24;
				margin-left: 2px;
			}
		}

		.form-item-content {
			width: 100%;
		}
	}

	.checkbox-item {
		.checkbox-group {
			display: flex;
			flex-wrap: wrap;

			.van-checkbox {
				margin-right: 16px;
				margin-bottom: 8px;
			}

			:deep(.van-checkbox__label) {
				color: #576B95;
				font-size: 14px;
			}
		}

		.empty-text {
			color: #c8c9cc;
			font-size: 14px;
		}
	}

	.textarea-item {
		.content-textarea {
			padding: 0;
			background-color: #fff;

			:deep(.van-field__control) {
				text-align: left;
				color: #333;
				min-height: 140px;
			}
		}
	}

	.file-item {
		.empty-file {
			color: #333;
			font-size: 14px;
		}
	}

	.gap {
		height: 10px;
		background-color: #f8f8f8;
	}

	.upload-container {
		display: flex;
		flex-wrap: wrap;
		padding: 10px 0;

		.upload-item {
			margin-right: 10px;
			margin-bottom: 10px;
		}

		.case-image {
			border-radius: 4px;
			overflow: hidden;
		}
	}
}
</style>
