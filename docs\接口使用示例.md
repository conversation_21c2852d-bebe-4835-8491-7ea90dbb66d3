# 定位回传接口使用示例

## 接口信息

**接口地址**: `/zhcg/personPosition`  
**请求方法**: `POST`  
**接口说明**: 用于上报人员位置信息

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| lat | string | 是 | 纬度，字符串格式 |
| lon | string | 是 | 经度，字符串格式 |
| address | string | 是 | 详细地址信息 |

## 请求示例

### JavaScript (使用现有API函数)

```javascript
import { personPosition } from '@/api/staffLocation'

// 上报位置信息
async function reportLocation() {
  try {
    const params = {
      lat: "30.274085",
      lon: "120.155070", 
      address: "浙江省杭州市西湖区文三路"
    }
    
    const response = await personPosition(params)
    console.log('位置上报成功:', response)
  } catch (error) {
    console.error('位置上报失败:', error)
  }
}
```

### 原始HTTP请求

```javascript
// 使用fetch API
fetch('/zhcg/personPosition', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    lat: "30.274085",
    lon: "120.155070",
    address: "浙江省杭州市西湖区文三路"
  })
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error))
```

## 在站前考勤中的使用

### 定时器回调中的位置上报

```javascript
// 上报当前位置
async reportCurrentLocation() {
  if (!this.subLng || !this.subLat) {
    console.log('无位置信息，跳过上报')
    return
  }

  try {
    const params = {
      lat: this.subLat.toString(),
      lon: this.subLng.toString(),
      address: this.addressText || '位置获取中...'
    }

    await personPosition(params)
    console.log('位置上报成功:', params)
  } catch (error) {
    console.error('位置上报失败:', error)
  }
}
```

### 定时器集成

```javascript
// 定位上报回调函数
locationReportCallback(data) {
  console.log('定位定时器触发:', data)
  this.reportCurrentLocation()
}

// 启动定位定时器
startLocationTimer() {
  locationTimer.start({
    userId: this.userId,
    userType: this.userType,
    callback: this.locationReportCallback
  })
  
  this.locationTimerActive = true
  console.log('定位定时器已启动')
}
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "12345",
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null
}
```

## 注意事项

1. **坐标格式**: 经纬度必须为字符串格式，不能是数字
2. **地址信息**: 建议提供详细的地址信息，便于后续查看和分析
3. **网络异常**: 需要处理网络请求失败的情况
4. **频率控制**: 定时器每5秒调用一次，注意服务器负载

## 错误处理

```javascript
async function safeReportLocation(lat, lon, address) {
  try {
    // 参数验证
    if (!lat || !lon) {
      throw new Error('经纬度不能为空')
    }
    
    if (typeof lat !== 'string' || typeof lon !== 'string') {
      lat = lat.toString()
      lon = lon.toString()
    }
    
    const params = { lat, lon, address: address || '未知位置' }
    
    // 发送请求
    const response = await personPosition(params)
    
    return { success: true, data: response }
  } catch (error) {
    console.error('位置上报失败:', error)
    return { success: false, error: error.message }
  }
}
```

## 测试方法

### 1. 使用测试页面
访问 `/TimerTest` 页面，点击"测试位置上报"按钮

### 2. 浏览器控制台测试
```javascript
// 在浏览器控制台中执行
import { personPosition } from '@/api/staffLocation'

personPosition({
  lat: "30.274085",
  lon: "120.155070",
  address: "测试地址"
}).then(res => console.log(res))
```

### 3. 模拟定时器测试
```javascript
// 模拟定时器每5秒上报一次
setInterval(() => {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(position => {
      personPosition({
        lat: position.coords.latitude.toString(),
        lon: position.coords.longitude.toString(),
        address: '定时器测试位置'
      })
    })
  }
}, 5000)
```

## 相关文件

- **API定义**: `src/api/staffLocation.js`
- **站前考勤**: `src/views/clockIn/StationFrontAttendance/index.vue`
- **定时器服务**: `src/util/locationTimer.js`
- **测试页面**: `src/views/clockIn/TimerTest.vue`
