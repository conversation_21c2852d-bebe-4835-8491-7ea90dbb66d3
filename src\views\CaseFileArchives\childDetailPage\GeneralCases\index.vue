<!--一般案件-->
<template>
	<div class="general-cases">

    <!-- 导航栏 -->
    <van-nav-bar
        title="一般案件"
        left-arrow
        fixed
        @click-left="$router.go(-1)"
        class="custom-nav"
    />

		<van-form class="container">
			<div class="form-section">
				<van-field
					v-model="form.party"
					label="当事人姓名:"
					readonly
					placeholder="请输入当事人姓名"
					class="form-field"
				/>
				<van-field
					v-model="form.phone"
					label="联系电话:"
					readonly
					placeholder="请输入当事人联系电话"
					class="form-field"
				/>
				<div class="form-item">
					<div class="form-item-label">当事人性别:</div>
					<div class="form-item-content">
						<van-radio-group v-model="form.gender" direction="horizontal" disabled class="gender-radio">
							<van-radio name="1">男</van-radio>
							<van-radio name="2">女</van-radio>
						</van-radio-group>
					</div>
				</div>
				<van-field
					v-model="form.age"
					label="年龄:"
					readonly
					type="number"
					placeholder="请输入当事人年龄"
					class="form-field"
				/>
			</div>
			<!-- 间隔 -->
			<div class="form-section">
				<van-field
					v-model="form.caseTime"
					label="案发时间:"
					readonly
					placeholder="请选择案发时间"
					class="form-field"
				>
					<template #right-icon><van-icon name="calendar-o" size="20px" color="#999" /></template>
				</van-field>
				<van-field
					v-model="form.summaryName"
					label="案由:"
					readonly
					placeholder="请选择案由"
					class="form-field"
				/>
				<van-field
					v-model="form.address"
					label="案发地址:"
					readonly
					placeholder="请选择地址"
					class="form-field"
				/>
        <van-field
            v-model="form.caseContent"
            type="textarea"
            autosize
            label="立案简介:"
            readonly
            class="form-field"
        />
			</div>

			<div class="form-section">
				<div class="form-item">
					<div class="form-item-label">处罚类型:</div>
					<div class="form-item-content">
						<span class="punish-type">罚款</span>
					</div>
				</div>
				<div class="form-item">
					<div class="form-item-label">处罚金额:</div>
					<div class="form-item-content money-field">
						<span class="money-symbol">￥</span>
						<span class="money-value">{{ form.punishMoney }}</span>
					</div>
				</div>
				<van-field
					v-model="form.userName"
					label="主办人:"
					readonly
					placeholder="请选择协办人"
					class="form-field"
				/>
				<van-field
					v-model="form.userNames"
					label="协办人:"
					readonly
					placeholder="请选择协办人"
					class="form-field"
				/>
        <van-field
            v-model="form.remark"
            type="textarea"
            autosize
            label="处罚结果:"
            readonly
            class="form-field"
        />
			</div>
		</van-form>
		<!-- 提示 -->
		<van-toast id="van-toast" />
	</div>
</template>

<script>
import { Toast } from 'vant';
import { getFileList } from "@/api/common";
import { getGeneral } from "@/api/GeneralCases";

export default {
	data() {
		return {
			form:{userType:1,isSelfEmployed:1,gender:1,type:0,punishType:0,onlineCarTarnsport:0,onlineCarDriver:0},
			showCaseDate:false,
			params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
			rules: {
				party: [{ required: true, message: '请输入当事人', trigger: 'blur' }],
				companyName: [{ required: true, message: '请填写单位名称', trigger: 'blur' }],
				companyAddress: [{ required: true, message: '请填写单位地址', trigger: 'change' }],
				companyCode: [{ required: true, message: '请填写单位统一社会信用代码', trigger: 'blur' }],
				caseTime: [{ required: true, message: '请选择案发时间', trigger: 'change' }],
				summaryId: [{ required: true, message: '请选择案由', trigger: 'change' }],
				phone: [
					{ required: true, message: '请输入当事人联系电话', trigger: 'blur' },
				],
				address: [{ required: true, message: '请选择地址', trigger: 'change' }],
				punishMoney: [{ required: true,type:'number',message: '请输入金额', trigger: 'blur' }],
				caseContent:[{ required: true, message: '请输入店铺名称', trigger: 'change' }],
			},
			happenData: {
				tableName: 'case_general',
				status: 2
			},
			happenFile:[],
			resFile: []
		}
	},
	computed: {

	},
	mounted() {
		this.init(this.$route.query);
	},
	methods: {
		async init(params) {
			Toast.loading({
				message: '加载中...',
				forbidClick: true,
			});

			if(params.id){
				Promise.all([
					getGeneral(params.id),
					getFileList({ tableName: 'case_general', businessId: params.id }),
				]).then(resAry => {
					const formData = resAry[0].data
					const lnglat = `${formData.longitude || ''},${formData.latitude || ''}`
					this.form = { ...formData, lnglat }
					console.log(resAry[0]);
					resAry[1].rows.map(item => {
						if (item.status == 2) {
							this.happenFile.push({ url: `${process.env.VUE_APP_BASE_API}${item.filePath}?id=${item.fileId}` })
						} else if (item.status == 9) {
							this.resFile.push({ url: `${process.env.VUE_APP_BASE_API}${item.filePath}?id=${item.fileId}` })
						}
					})
					Toast.clear();
				}).catch((err) => {
					console.log(err);
					Toast.clear();
					Toast.fail('加载失败');
				})
			}else{
				const timestamp = new Date().getTime()
				const caseTime = this.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
				this.form = {...this.form,caseTime, userName: this.user.userName, userId: this.user.userId }
				Toast.clear();
			}
		},
		// 预览图片
		previewImage(fileList, index) {
			const images = fileList.map(item => item.url);
			this.$imagePreview({
				images,
				startPosition: index,
				closeable: true
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.general-cases {
	padding-top: 46px;
	min-height: 100vh;
	background-color: #f8f8f8;

	.custom-nav {
		background-color: #fff;
		height: 44px;

		:deep(.van-nav-bar__title) {
			font-size: 16px;
			font-weight: 400;
			color: #000;
		}

		:deep(.van-icon) {
			color: #000;
		}
	}

	.container {
		background-color: #f8f8f8;
		padding: 0;
	}

	.form-section {
		padding: 0;
		background-color: #fff;
		margin-bottom: 8px;
	}

	.form-field {
		padding: 10px 16px;
		margin: 0;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 16px;
			right: 16px;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
			transform: scaleY(0.5);
		}

		:deep(.van-field__label) {
			width: 110px;
			color: #000;
			font-size: 14px;
		}

		:deep(.van-cell__value) {
			flex: 1;
			text-align: right;
			overflow: visible;
		}

		:deep(.van-field__control) {
			color: #576B95;
			text-align: right;
			font-size: 14px;
		}
	}

	.form-item {
		display: flex;
		align-items: center;
		min-height: 44px;
		padding: 10px 16px;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 16px;
			right: 16px;
			bottom: 0;
			height: 1px;
			background-color: #f2f2f2;
			transform: scaleY(0.5);
		}

		.form-item-label {
			width: 110px;
			color: #000;
			font-size: 14px;
		}

		.form-item-content {
			flex: 1;
			text-align: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
			color: #576B95;
			font-size: 14px;
		}
	}

	.gender-radio {
		:deep(.van-radio__icon) {
			font-size: 16px;
		}

		:deep(.van-radio__label) {
			color: #000;
			font-size: 14px;
			margin-left: 4px;
		}

		:deep(.van-radio) {
			margin-left: 16px;
		}
	}

	.case-detail-box {
		padding: 10px 16px;
		background-color: #f8f8f8;

		.case-detail-content {
			color: #000;
			font-size: 14px;
			line-height: 1.5;
			white-space: pre-wrap;
			word-break: break-all;
		}
	}

	.punish-result-box {
		padding: 10px 16px;
		background-color: #f8f8f8;

		.punish-result-content {
			color: #000;
			font-size: 14px;
			line-height: 1.5;
			white-space: pre-wrap;
			word-break: break-all;
		}
	}

	.money-field {
		.money-symbol {
			color: #000;
			font-size: 14px;
		}

		.money-value {
			color: #FF0000;
			font-size: 14px;
		}
	}

	.punish-type {
		color: #576B95;
	}

	.upload-container {
		display: flex;
		flex-wrap: wrap;
		padding: 10px 16px;

		.upload-item {
			margin-right: 10px;
			margin-bottom: 10px;
		}

		.case-image {
			border-radius: 4px;
			overflow: hidden;
		}
	}
}
</style>


