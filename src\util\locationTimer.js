/**
 * 定位定时器管理服务
 * 用于站前考勤打卡后的定时定位上报
 */

class LocationTimer {
  constructor() {
    this.timer = null
    this.isRunning = false
    this.interval = 5000 // 5秒间隔
    this.callbacks = []
    this.userId = null
    this.userType = null
    this.startTime = null

    // 监听页面可见性变化
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this)
    document.addEventListener('visibilitychange', this.handleVisibilityChange)

    // 监听页面卸载
    window.addEventListener('beforeunload', () => {
      this.saveTimerState()
    })

    // 应用启动时恢复定时器状态
    this.restoreTimerState()
  }

  /**
   * 启动定时器
   * @param {Object} options 配置选项
   * @param {string} options.userId 用户ID
   * @param {string} options.userType 用户类型
   * @param {Function} options.callback 定时回调函数
   */
  start(options = {}) {
    const { userId, userType, callback } = options

    if (this.isRunning) {
      console.log('定位定时器已在运行中')
      return
    }

    this.userId = userId
    this.userType = userType
    this.startTime = new Date().toISOString()

    if (callback && typeof callback === 'function') {
      this.callbacks.push(callback)
    }

    this.isRunning = true

    // 立即执行一次
    this.executeCallbacks()

    // 启动定时器
    this.timer = setInterval(() => {
      this.executeCallbacks()
    }, this.interval)

    // 保存状态到本地存储
    this.saveTimerState()

    console.log(`定位定时器已启动，间隔: ${this.interval}ms`)
  }

  /**
   * 停止定时器
   */
  stop() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    this.isRunning = false
    this.callbacks = []
    this.startTime = null

    // 清除本地存储的状态
    this.clearTimerState()

    console.log('定位定时器已停止')
  }

  /**
   * 添加回调函数
   * @param {Function} callback 回调函数
   */
  addCallback(callback) {
    if (typeof callback === 'function') {
      this.callbacks.push(callback)
    }
  }

  /**
   * 移除回调函数
   * @param {Function} callback 回调函数
   */
  removeCallback(callback) {
    const index = this.callbacks.indexOf(callback)
    if (index > -1) {
      this.callbacks.splice(index, 1)
    }
  }

  /**
   * 执行所有回调函数
   */
  executeCallbacks() {
    if (!this.isRunning) return

    // 执行全局定位上报
    this.executeGlobalLocationReport()

    // 执行其他回调函数
    this.callbacks.forEach(callback => {
      try {
        callback({
          timestamp: new Date().toISOString(),
          userId: this.userId,
          userType: this.userType,
          startTime: this.startTime
        })
      } catch (error) {
        console.error('定位定时器回调执行失败:', error)
      }
    })
  }

  /**
   * 执行全局定位上报
   */
  async executeGlobalLocationReport() {
    try {
      // 获取当前位置
      const position = await this.getCurrentPosition()

      if (position) {
        // 动态导入API模块
        const { personPosition } = await import('@/api/staffLocation')

        const params = {
          lat: position.latitude.toString(),
          lon: position.longitude.toString(),
          address: position.address || '位置获取中...'
        }

        await personPosition(params)
        console.log('全局定位上报成功:', params)
      }
    } catch (error) {
      console.error('全局定位上报失败:', error)
    }
  }

  /**
   * 获取当前位置
   */
  getCurrentPosition() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('浏览器不支持地理位置获取'))
        return
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords

          try {
            // 获取地址信息
            const address = await this.getAddressFromCoords(latitude, longitude)

            resolve({
              latitude,
              longitude,
              address
            })
          } catch (error) {
            // 即使地址获取失败，也返回坐标信息
            resolve({
              latitude,
              longitude,
              address: `${latitude}, ${longitude}`
            })
          }
        },
        (error) => {
          console.error('获取位置失败:', error)
          reject(error)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 30000
        }
      )
    })
  }

  /**
   * 根据坐标获取地址信息
   */
  async getAddressFromCoords(lat, lng) {
    try {
      // 使用高德地图逆地理编码API
      const response = await fetch(
        `https://restapi.amap.com/v3/geocode/regeo?key=YOUR_AMAP_KEY&location=${lng},${lat}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`
      )

      if (response.ok) {
        const data = await response.json()
        if (data.status === '1' && data.regeocode) {
          return data.regeocode.formatted_address
        }
      }
    } catch (error) {
      console.error('地址解析失败:', error)
    }

    // 如果地址解析失败，返回坐标信息
    return `纬度: ${lat}, 经度: ${lng}`
  }

  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.hidden) {
      // 页面隐藏时暂停定时器（但保持状态）
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      console.log('页面隐藏，定时器暂停')
    } else {
      // 页面显示时恢复定时器
      if (this.isRunning && !this.timer) {
        this.timer = setInterval(() => {
          this.executeCallbacks()
        }, this.interval)
        console.log('页面显示，定时器恢复')
      }
    }
  }

  /**
   * 保存定时器状态到本地存储
   */
  saveTimerState() {
    if (this.isRunning) {
      const state = {
        isRunning: this.isRunning,
        userId: this.userId,
        userType: this.userType,
        startTime: this.startTime,
        interval: this.interval
      }

      localStorage.setItem('locationTimerState', JSON.stringify(state))
    }
  }

  /**
   * 从本地存储恢复定时器状态
   */
  restoreTimerState() {
    try {
      const stateStr = localStorage.getItem('locationTimerState')
      if (stateStr) {
        const state = JSON.parse(stateStr)

        if (state.isRunning && state.userId) {
          console.log('检测到定时器状态，准备恢复...')

          // 检查打卡状态以确定是否需要恢复定时器
          this.checkClockStatusAndRestore(state)
        }
      }
    } catch (error) {
      console.error('恢复定时器状态失败:', error)
      this.clearTimerState()
    }
  }

  /**
   * 检查打卡状态并恢复定时器
   * @param {Object} state 保存的状态
   */
  async checkClockStatusAndRestore(state) {
    try {
      // 这里需要调用API检查当前打卡状态
      // 如果已上班打卡但未下班打卡，则恢复定时器
      const response = await this.getClockStatus(state.userId, state.userType)

      if (response && this.shouldRestoreTimer(response)) {
        this.userId = state.userId
        this.userType = state.userType
        this.startTime = state.startTime
        this.interval = state.interval || 5000
        this.isRunning = true

        // 启动定时器（不执行回调，等待页面组件注册回调）
        this.timer = setInterval(() => {
          this.executeCallbacks()
        }, this.interval)

        console.log('定时器状态已恢复')
      } else {
        // 如果不需要恢复定时器，清除状态
        this.clearTimerState()
      }
    } catch (error) {
      console.error('检查打卡状态失败:', error)
      this.clearTimerState()
    }
  }

  /**
   * 获取打卡状态
   * @param {string} userId 用户ID
   * @param {string} userType 用户类型
   */
  async getClockStatus(userId, userType) {
    try {
      // 动态导入API模块以避免循环依赖
      const { getCurrentClockStatus } = await import('@/api/clockIn')
      const response = await getCurrentClockStatus(userId, userType)

      if (response && response.data) {
        const data = response.data
        const today = new Date().toISOString().split('T')[0]

        return {
          hasStartClock: !!data.actualStartTime,  // 是否已上班打卡
          hasEndClock: !!data.actualEndTime,      // 是否已下班打卡
          clockDate: data.punchTime ? data.punchTime.split(' ')[0] : today, // 打卡日期
          punchId: data.punchId
        }
      }

      return null
    } catch (error) {
      console.error('获取打卡状态失败:', error)
      return null
    }
  }

  /**
   * 判断是否应该恢复定时器
   * @param {Object} clockStatus 打卡状态
   */
  shouldRestoreTimer(clockStatus) {
    const today = new Date().toISOString().split('T')[0]

    // 如果是今天，且已上班打卡但未下班打卡，则恢复定时器
    return clockStatus.clockDate === today &&
           clockStatus.hasStartClock &&
           !clockStatus.hasEndClock
  }

  /**
   * 清除本地存储的定时器状态
   */
  clearTimerState() {
    localStorage.removeItem('locationTimerState')
  }

  /**
   * 获取定时器状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      userId: this.userId,
      userType: this.userType,
      startTime: this.startTime,
      interval: this.interval,
      callbackCount: this.callbacks.length
    }
  }

  /**
   * 销毁定时器实例
   */
  destroy() {
    this.stop()
    document.removeEventListener('visibilitychange', this.handleVisibilityChange)
    window.removeEventListener('beforeunload', this.saveTimerState)
  }
}

// 创建全局单例
const locationTimer = new LocationTimer()

export default locationTimer
