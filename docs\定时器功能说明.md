# 全局定位追踪功能说明

## 功能概述

全局定位追踪功能实现了在用户上班打卡后，无论在应用的哪个页面，都会每5秒自动获取并上报当前位置信息，直到用户下班打卡后停止定时器。当应用重新启动时，系统会自动检查当前账号的打卡状态，如果检测到已上班但未下班，会自动恢复定时器功能。

## 核心特性

### 1. 全局定时上报
- ✅ 上班打卡成功后自动启动全局定时器
- ✅ 每5秒获取一次当前位置并上报到服务器
- ✅ 在任何页面都会持续上报位置信息
- ✅ 下班打卡成功后自动停止定时器

### 2. 应用重启恢复
- ✅ 应用关闭重新打开时自动检查打卡状态
- ✅ 如果已上班但未下班，自动恢复定时器
- ✅ 状态持久化存储，确保数据不丢失

### 3. 页面可见性管理
- ✅ 页面隐藏时暂停定时器，显示时恢复
- ✅ 优化电池使用，减少后台资源消耗
- ✅ 支持多标签页和应用切换

### 4. 用户界面指示
- ✅ 实时显示定时器运行状态
- ✅ 脉冲动画指示器，直观展示上报状态
- ✅ 详细的状态信息展示

## 技术实现

### 核心文件

#### 1. 定时器管理服务 (`src/util/locationTimer.js`)
```javascript
// 全局单例定时器管理器
import locationTimer from '@/util/locationTimer'

// 启动定时器
locationTimer.start({
  userId: 'user123',
  userType: '1',
  callback: (data) => {
    console.log('定时器触发:', data)
  }
})

// 停止定时器
locationTimer.stop()
```

#### 2. API接口
```javascript
// 获取当前打卡状态 (src/api/clockIn.js)
getCurrentClockStatus(userId, userType)

// 上报位置信息 (src/api/staffLocation.js)
personPosition(data)
```

#### 3. 站前考勤组件 (`src/views/clockIn/StationFrontAttendance/index.vue`)
- 集成定时器功能
- 打卡成功后自动启动/停止定时器
- 页面加载时检查并恢复定时器状态

### 工作流程

#### 上班打卡流程
1. 用户点击"上班打卡"
2. 验证位置信息和打卡区域
3. 调用打卡API
4. 打卡成功后自动启动定时器
5. 开始每5秒上报位置信息

#### 下班打卡流程
1. 用户点击"下班打卡"
2. 验证位置信息和打卡区域
3. 调用打卡API
4. 打卡成功后自动停止定时器
5. 清除本地存储的定时器状态

#### 应用重启恢复流程
1. 应用启动时检查本地存储
2. 发现定时器状态记录
3. 调用API验证当前打卡状态
4. 如果已上班但未下班，恢复定时器
5. 重新开始位置上报

## 使用方法

### 基本使用

1. **正常打卡流程**
   - 进入站前考勤页面
   - 确保位置权限已开启
   - 在考勤区域内进行上班打卡
   - 系统自动启动定时器，显示"定位上报中"状态
   - 下班时进行下班打卡，系统自动停止定时器

2. **应用重启后**
   - 重新打开应用
   - 进入站前考勤页面
   - 系统自动检查打卡状态
   - 如果需要，自动恢复定时器功能

### 状态指示器

页面上会显示以下状态信息：

- **考勤区域状态**: 显示是否在考勤区域内
- **定位上报状态**: 当定时器运行时显示"定位上报中 (每5秒)"
- **脉冲指示器**: 蓝色圆点闪烁，表示定时器正在工作
- **位置信息**: 显示当前详细地址

### 测试功能

访问 `/TimerTest` 页面可以测试定时器功能：

- 查看定时器当前状态
- 手动启动/停止定时器
- 查看定时器触发日志
- 测试状态恢复功能

## API接口说明

### 1. 获取打卡状态
```javascript
GET /business/punchIn/getOfficeTodayInfo/{userId}  // 机关人员
GET /business/punchIn/getGridTodayInfo/{userId}    // 网格人员

Response:
{
  "data": {
    "punchId": "123",
    "actualStartTime": "2024-01-15 09:00:00",
    "actualEndTime": null,
    "punchTime": "2024-01-15"
  }
}
```

### 2. 上报位置信息
```javascript
POST /zhcg/personPosition

Request:
{
  "lat": "30.123456",
  "lon": "120.123456",
  "address": "浙江省杭州市西湖区..."
}
```

## 注意事项

### 1. 权限要求
- 需要位置权限（GPS/网络定位）
- 需要后台运行权限（部分设备）

### 2. 网络要求
- 需要稳定的网络连接进行位置上报
- 网络异常时会在控制台记录错误日志

### 3. 电池优化
- 页面隐藏时会暂停定时器
- 避免在后台持续消耗电量

### 4. 数据存储
- 定时器状态存储在 localStorage
- 应用卸载后状态会丢失

## 故障排除

### 1. 定时器未启动
- 检查是否成功上班打卡
- 确认位置权限已开启
- 查看控制台错误信息

### 2. 位置上报失败
- 检查网络连接
- 确认GPS信号正常
- 查看API接口是否正常

### 3. 应用重启后未恢复
- 检查本地存储是否被清除
- 确认打卡状态API正常
- 查看控制台恢复日志

## 开发调试

### 1. 控制台日志
定时器会在控制台输出详细日志：
```
定位定时器已启动，间隔: 5000ms
定位定时器触发: {timestamp: "...", userId: "..."}
位置上报成功: {longitude: 120.123, latitude: 30.123}
```

### 2. 本地存储
检查 localStorage 中的 `locationTimerState` 键：
```javascript
localStorage.getItem('locationTimerState')
```

### 3. 测试页面
使用 `/TimerTest` 页面进行功能测试和调试。

## 更新日志

### v1.0.0 (2024-01-15)
- 实现基础定时器功能
- 支持上班打卡后自动启动
- 支持下班打卡后自动停止
- 实现应用重启后状态恢复
- 添加用户界面状态指示器
- 支持页面可见性管理
