<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-06-18 08:42:28
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-18 08:42:39
-->
<template></template>
<script>
import dd from "gdt-jsapi";
export default {
  data() {
    return {
      dogId: null,
      verificationType: null,
    };
  },
  mounted() {
    console.log("kaishi");
    dd.getAuthCode({}).then(
      (result) => {
        if (result) {
          this.getZzdInfoByauthCode(result.code);
        }
      },
      (err) => {
        console.log(err);
      }
    );
  },
  methods: {
    getZzdInfoByauthCode(authCode) {
      baseObj
        .doGet("/zzd/getZzdInfoByauthCode?authCode=" + authCode)
        .then((res) => {
          if (res.data.code === 0) {
            // 登录成功  返回用户信息 直接登录
            setToken(res.token);
            this.GetInfo(res.token);
            this.$toast.success("登录成功");
            this.$router.push("/");
            // 跳转首页
          } else {
            this.$toast.fail(error.msg);
          }
        });
    },
    gobackByPageshow() {
      window.onpageshow = (event) => {
        console.log("navigation.type=" + window.performance.navigation.type);
        console.log("navigation.type= 11111");
        if (
          event.persisted ||
          (window.performance && window.performance.navigation.type == 2)
        ) {
          ZWJSBridge.close();
        }
      };
    },

    // 检查平台 是浙里办APP 还是支付宝
    checkZwPlatform() {
      console.log("3");

      const sUserAgent = window.navigator.userAgent.toLowerCase();
      // 浙里办APP
      const bIsDtDreamApp = sUserAgent.indexOf("dtdreamweb") > -1;
      // 支付宝小程序
      const bIsAlipayMini =
        sUserAgent.indexOf("miniprogram") > -1 &&
        sUserAgent.indexOf("alipay") > -1;

      let result = "";

      if (bIsDtDreamApp === true) {
        result = "app";
      } else if (bIsAlipayMini === true) {
        result = "mini";
      }
      return result;
    },
  },
};
</script>

<style lang="less" scoped="scoped">
.body-content {
  height: 100vh;
  // background-image: url(../static/img-login-bg.png);
  background-size: 100% 100%;
  color: #fff;
}

.login-title {
  // font-family: '仿宋';
  box-sizing: border-box;
  width: 30%;
  text-align: center;
  font-size: 40rpx;
  font-weight: bold;
  border-bottom: 4rpx solid rgba(255, 255, 255, 0.3);
  padding: 20vh 10rpx 16rpx;
  letter-spacing: 4rpx;
  margin-left: 60%;
}

.form-box {
  padding: 120rpx 80rpx;
}

.icon-login {
  width: 50rpx;
  height: 50rpx;
}

.cu-form-group {
  background-color: transparent;
  position: relative;
  padding: 0 10rpx;
  border-bottom: 4rpx solid rgba(255, 255, 255, 0.3);
}

.cu-form-group .title {
  display: flex;
  align-items: center;
}

.cu-form-group input {
  color: #fff;
}

.uni-input-placeholder {
  color: #fff;
}

.cu-form-group + .cu-form-group {
  border-top: none;
}

.login-btn {
  border-radius: 200rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  font-size: 30rpx;
  margin-top: 120rpx;
  background-color: transparent;
  color: #fff;
}

.yanzheng-box {
  position: absolute;
  right: 0;
  top: 16rpx;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #fff;
  color: #3e95fc;
  text-align: center;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.zhuce {
  padding: 30rpx 0 0;
  text-align: right;
}

.login-title-big {
  position: absolute;
  top: 8vh;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 46rpx;
}
</style>
