<!--人员详情-->
<template>
  <div class="staff-detail">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="人员详情"
      left-arrow
      fixed
      @click-left="onClickLeft"
    />

    <!-- 加载中遮罩 -->
    <div class="loading-container" v-if="loading">
      <van-loading type="spinner" color="#1989fa" size="24px" />
    </div>

    <template v-else>
      <!-- 个人信息卡片 -->
      <div class="user-card">
        <div class="avatar">
          <van-icon name="manager" color="#1989fa" size="24" />
        </div>
        <div class="name">{{ staffInfo.userName || '未知' }}</div>
        <div class="status-tag" :class="staffInfo.status == 1 ? 'status-online' : 'status-offline'">
          {{ staffInfo.status == 1 ? '在线' : '离线' }}
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="info-section">
        <div class="info-item">
          <div class="label">部门</div>
          <div class="value">{{ staffInfo.deptName || '暂无' }}</div>
        </div>
        <div class="info-item">
          <div class="label">角色</div>
          <div class="value">{{ staffInfo.roleName || '暂无' }}</div>
        </div>
        <div class="info-item">
          <div class="label">联系方式</div>
          <div class="value">{{ staffInfo.mobile || '暂无' }}</div>
        </div>
        <div class="info-item">
          <div class="label">当前位置</div>
          <div class="value">{{ staffInfo.address || '暂无位置信息' }}</div>
        </div>
        <div class="info-item">
          <div class="label">最后上报</div>
          <div class="value">{{ staffInfo.updateTime || '暂无' }}</div>
        </div>
      </div>

      <!-- 位置信息 -->
      <div class="map-section" v-if="staffInfo.lat && staffInfo.lon">
        <div class="section-title">位置信息</div>
        <div class="map-container" @click="showMapDialog = true">
          <img src="@/assets/common/map-placeholder.png" alt="地图" class="map-image" />
          <div class="map-marker"></div>
          <div class="map-tip">点击查看位置</div>
        </div>
        <div class="map-coords">
          经纬度: {{ staffInfo.lon }}, {{ staffInfo.lat }}
        </div>
      </div>

      <!-- 行动轨迹 -->
      <div class="track-section">
        <div class="section-title">人员行动轨迹</div>
        <div class="timeline" v-if="staffInfo.positionList && staffInfo.positionList.length > 0">
          <div class="timeline-item" v-for="(item, index) in staffInfo.positionList" :key="index">
            <div class="timeline-dot"></div>
            <div class="timeline-content">
              <div class="timeline-time">{{ item.createTime }}</div>
              <div class="timeline-desc">{{ item.address }}</div>
            </div>
          </div>
        </div>
        <div class="empty-tracks" v-else>
          <van-empty description="暂无行动轨迹" />
        </div>
      </div>
    </template>

    <!-- 地图弹窗 -->
    <van-popup
      v-model="showMapDialog"
      position="bottom"
      :style="{ height: '80%', width: '100%' }"
    >
      <div class="map-dialog">
        <Map
          v-if="showMapDialog"
          ref="mapComponent"
          :coordinates="staffCoordinates"
          :is-editable="false"
        />
        <div class="map-dialog-footer">
          <van-button type="primary" block @click="showMapDialog = false">关闭</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getPersonDetail } from "@/api/staffLocation";
import Map from '@/components/map/index.vue';

export default {
  name: "StaffDetail",
  components: {
    Map
  },
  data() {
    return {
      loading: true,
      staffId: null,
      staffInfo: {},
      showMapDialog: false,
      staffCoordinates: [],
    }
  },
  created() {
    // 从路由参数获取人员ID
    this.staffId = this.$route.query.id;
    if (!this.staffId) {
      this.$toast.fail('缺少人员ID参数');
      setTimeout(() => {
        this.onClickLeft();
      }, 1500);
      return;
    }
  },
  mounted() {
    this.getStaffDetail();
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },

    // 获取人员详情
    async getStaffDetail() {
      if (!this.staffId) return;

      try {
        this.loading = true;
        const res = await getPersonDetail(this.staffId);

        if (res.code === 200 && res.data) {
          this.staffInfo = res.data;

          // 如果有经纬度，设置地图坐标
          if (this.staffInfo.lon && this.staffInfo.lat) {
            this.staffCoordinates = [parseFloat(this.staffInfo.lon), parseFloat(this.staffInfo.lat)];
          }

          // 如果没有轨迹数据，初始化为空数组
          if (!this.staffInfo.trackList) {
            this.staffInfo.trackList = [];
          }

          console.log('获取人员详情成功:', this.staffInfo);
        } else {
          this.$toast.fail(res.msg || '获取人员详情失败');
        }
      } catch (error) {
        console.error('获取人员详情异常:', error);
        this.$toast.fail('获取人员详情失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-detail {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-top: 46px;

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 80px 0;
  }

  .user-card {
    display: flex;
    align-items: center;
    padding: 20px 15px 15px 15px;
    background-color: #fff;
    margin-bottom: 1px;
    position: relative;

    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background-color: #f0f9ff;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-left: 12px;
      flex: 1;
    }

    .status-tag {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 4px;

      &.status-online {
        color: #07c160;
        background-color: rgba(7, 193, 96, 0.1);
      }

      &.status-offline {
        color: #969799;
        background-color: rgba(150, 151, 153, 0.1);
      }
    }
  }

  .info-section {
    background-color: #fff;
    margin-bottom: 10px;

    .info-item {
      display: flex;
      padding: 12px 15px;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .label {
        width: 80px;
        color: #999;
        font-size: 14px;
      }

      .value {
        flex: 1;
        color: #333;
        font-size: 14px;
        word-break: break-all;

        &.status-online {
          color: #07c160;
        }

        &.status-offline {
          color: #969799;
        }
      }
    }
  }

  .map-section {
    background-color: #fff;
    padding: 15px;
    margin-bottom: 10px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .map-container {
      position: relative;
      height: 180px;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 10px;

      .map-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .map-marker {
        position: absolute;
        width: 18px;
        height: 18px;
        background-color: #1989fa;
        border: 2px solid #fff;
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .map-tip {
        position: absolute;
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 5px 12px;
        border-radius: 16px;
        font-size: 12px;
      }
    }

    .map-coords {
      font-size: 13px;
      color: #666;
    }
  }

  .track-section {
    background-color: #fff;
    padding: 15px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 15px;
    }

    .empty-tracks {
      padding: 20px 0;
    }

    .timeline {
      position: relative;

      &:before {
        content: '';
        position: absolute;
        left: 7px;
        top: 0;
        bottom: 0;
        width: 1px;
        background-color: #e8e8e8;
      }

      .timeline-item {
        position: relative;
        display: flex;
        padding-bottom: 20px;

        &:last-child {
          padding-bottom: 0;
        }

        .timeline-dot {
          position: relative;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background-color: #fff;
          border: 1px solid #1989fa;
          margin-right: 15px;
          z-index: 1;

          &:before {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 7px;
            height: 7px;
            border-radius: 50%;
            background-color: #1989fa;
          }
        }

        .timeline-content {
          flex: 1;

          .timeline-time {
            font-size: 14px;
            color: #1989fa;
            margin-bottom: 5px;
          }

          .timeline-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
            word-break: break-all;
          }
        }
      }
    }
  }

  // 地图弹窗样式
  .map-dialog {
    height: 100%;
    display: flex;
    flex-direction: column;

    &-footer {
      padding: 10px;
      background: #fff;
      box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }
  }
}
</style>
