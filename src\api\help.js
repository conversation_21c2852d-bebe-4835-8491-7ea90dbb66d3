import request from '@/util/request'

/**
 * 获取帮助文档列表
 */
export function getDocumentList() {
  // 返回本地文档列表
  return Promise.resolve({
    data: [
      {
        id: 1,
        title: '三色预警操作手册',
        description: '金华市城市运行管理服务系统三色预警功能详细操作指南',
        filename: '金华市城市运行管理服务系统项目操作手册-三色预警.docx',
        size: '2.5MB',
        updateTime: '2024-01-10',
        type: 'docx'
      },
      {
        id: 2,
        title: '犬类管理操作手册',
        description: '犬类管理模块的详细操作说明和流程指导',
        filename: '金华市城市运行管理服务系统项目操作手册-犬类管理.docx',
        size: '1.8MB',
        updateTime: '2024-01-10',
        type: 'docx'
      },
      {
        id: 3,
        title: '监督指挥操作手册',
        description: '监督指挥功能模块的使用说明和操作流程',
        filename: '金华市城市运行管理服务系统项目操作手册-监督指挥.docx',
        size: '2.1MB',
        updateTime: '2024-01-10',
        type: 'docx'
      },
      {
        id: 4,
        title: '站前管理PC端操作手册',
        description: '站前管理PC端系统的详细操作指南',
        filename: '金华市城市运行管理服务系统项目操作手册-站前管理PC端.docx',
        size: '3.2MB',
        updateTime: '2024-01-10',
        type: 'docx'
      },
      {
        id: 5,
        title: '站前管理移动端操作手册',
        description: '站前管理移动端应用的使用说明和操作指导',
        filename: '金华市城市运行管理服务系统项目操作手册-站前管理移动端.docx',
        size: '2.8MB',
        updateTime: '2024-01-10',
        type: 'docx'
      }
    ]
  })
}

/**
 * 获取文档内容
 * @param {string} filename 文档文件名
 */
export function getDocumentContent(filename) {
  // 对于Word文档，返回文档信息而不是内容
  if (filename.endsWith('.docx')) {
    return Promise.resolve({
      data: {
        filename,
        type: 'docx',
        downloadUrl: `/src/views/personCenter/docs/${filename}`
      }
    })
  }

  return request({
    url: `/help/document/${filename}`,
    method: 'get'
  })
}

/**
 * 搜索文档
 * @param {string} keyword 搜索关键词
 */
export function searchDocuments(keyword) {
  return request({
    url: '/help/search',
    method: 'get',
    params: {
      keyword
    }
  })
}

/**
 * 记录文档访问
 * @param {number} documentId 文档ID
 */
export function recordDocumentView(documentId) {
  return request({
    url: '/help/view',
    method: 'post',
    data: {
      documentId
    }
  })
}

/**
 * 获取热门文档
 */
export function getPopularDocuments() {
  return request({
    url: '/help/popular',
    method: 'get'
  })
}

/**
 * 提交反馈
 * @param {object} feedback 反馈内容
 */
export function submitFeedback(feedback) {
  return request({
    url: '/help/feedback',
    method: 'post',
    data: feedback
  })
}
